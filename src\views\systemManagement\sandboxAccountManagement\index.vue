<template>
    <div class="sandbox-account">
        <h2 class="page-title">沙箱账号管理</h2>

        <!-- 操作按钮 -->
        <div class="operation-bar" style="display: flex; justify-content: space-between;">
            <div>
                <el-button type="primary" @click="addFn" v-hasPermi="['system:sandboxUser:add']">
                    <el-icon>
                        <Plus />
                    </el-icon>
                    新建账号
                </el-button>
            </div>

            <div style="width: 300px;">
                <el-input v-model="searchQuery" placeholder="搜索账号..." class="w-64" clearable
                    @keyup.enter="applyFilters">
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-card class="table-card">
            <el-table :data="accounts" style="width: 100%" v-loading="loading">
                <el-table-column type="index" label="序号" width="80" />
                <el-table-column prop="sandboxName" label="沙箱名称" />
                <el-table-column prop="website" label="地址" />
                <el-table-column prop="userName" label="账号" />
                <el-table-column label="密码">
                    <template #default="{ row }">
                        <span class="password-mask">{{ row.showPassword ? row.password : '********' }}</span>
                        <el-button link type="primary" @click="showPassword(row)">
                            {{ row.showPassword ? '隐藏' : '查看' }}
                        </el-button>
                    </template>
                </el-table-column>
               
                <el-table-column label="数据库地址" align="center">
                    <template #default="{ row }">
                        {{ row.dbUrl }}
                    </template>
                </el-table-column>
                <el-table-column label="数据库端口" align="center">
                    <template #default="{ row }">
                        {{ row.dbPort }}
                    </template>
                </el-table-column>
                <el-table-column label="数据库用户名" align="center">
                    <template #default="{ row }">
                        {{ row.dbName }}
                    </template>
                </el-table-column>
                <el-table-column label="数据库密码" align="center">
					<template #default="{ row }">
					    <span class="password-mask">{{ row.showPassword2 ? row.dbPassword : '********' }}</span>
					    <el-button link type="primary" @click="showPassword2(row)">
					        {{ row.showPassword2 ? '隐藏' : '查看' }}
					    </el-button>
					</template>
                </el-table-column>
				<el-table-column prop="status" label="当前状态">
				    <template #default="{ row }">
				        <el-tag :type="getStatusType(row.statusStr)">{{ row.statusStr }}</el-tag>
				    </template>
				</el-table-column>
                <el-table-column label="操作" width="250">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="editRole(row)"
                            v-hasPermi="['system:sandboxUser:edit']">编辑</el-button>
                        <!-- v-hasPermi="['system:sandboxUser:edit']" -->
                        <el-button type="primary" link @click="delRole(row)"
                            v-hasPermi="['system:sandboxUser:remove']">删除</el-button>
                        <!-- v-hasPermi="['system:sandboxUser:remove']" -->
                        <el-button :type="row.status == 0 ? 'danger' : 'success'" link @click="toggleRoleStatus(row)"
                            v-hasPermi="['system:sandboxUser:status']">
                            {{ row.status == 0 ? '禁用' : '启用' }}
                        </el-button>
                        <el-button link type="primary" @click="editPassword(row)"
                            v-hasPermi="['system:sandboxUser:pwd']">更改密码</el-button>
                        <!-- <el-button link type="primary" @click="toggleStatus(row)">设置状态</el-button> -->
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="mt-6 flex justify-end">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalItems"
                    :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" class="custom-pagination" />
            </div>
        </el-card>

        <!-- 新建账号对话框 -->
        <el-dialog v-model="showNewAccountDialog" title="新建账号" width="500px" @close="cancelFn">
            <el-form :model="newAccount" label-width="100px" :rules="rules" ref="newForm">
                <el-form-item label="沙箱名称" prop="sandboxId">
                    <!-- <el-select v-model="newAccount.sandboxName" placeholder="请选择沙箱" style="width: 100%">
                        <el-option v-for="sandbox in sandboxes" :key="sandbox.id" :label="sandbox.sandboxName"
                            :value="sandbox.id" />
                    </el-select> -->
                    <el-input v-model="newAccount.sandboxName" clearable style="width: 100%" />
                </el-form-item>
                <el-form-item label="地址" prop="address">
                    <el-input v-model="newAccount.address" clearable style="width: 100%" />
                </el-form-item>
                <el-form-item label="账号" prop="username">
                    <el-input v-model="newAccount.username" clearable  style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input v-model="newAccount.password" clearable  type="password" style="width: 100%"
                        autocomplete="new-password"  show-password/>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="newAccount.status"  style="width: 100%">
                        <el-option v-for="status in statusOptions" :key="status.dictLabel" :label="status.dictLabel"
                            :value="status.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label=" 数据库地址" prop="dbUrl">
                    <el-input v-model="newAccount.dbUrl" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库端口" prop="dbPort">
                    <el-input v-model="newAccount.dbPort" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库用户名" prop="dbName">
                    <el-input v-model="newAccount.dbName" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库密码" prop="dbPassword">
                    <el-input v-model="newAccount.dbPassword" clearable style="width: 100%" autocomplete="new-password"
                        show-password />
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelFn">取消</el-button>
                    <el-button type="primary" @click="createAccount">创建</el-button>
                </span>
            </template>
        </el-dialog>


        <!-- 编辑账号对话框 -->
        <el-dialog v-model="showEditAccountDialog" title="编辑账号" width="500px" @close="cancelFn2">
            <el-form :model="editAccount" label-width="100px" :rules="rules" ref="editForm">
                <el-form-item label="沙箱名称" prop="sandboxId">
                    <!-- <el-select v-model="editAccount.sandboxId" placeholder="请选择沙箱" style="width: 100%">
                        <el-option v-for="sandbox in sandboxes" :key="sandbox.id" :label="sandbox.sandboxName"
                            :value="sandbox.id" />
                    </el-select> -->
                    <el-input v-model="editAccount.sandboxName" clearable style="width: 100%" />
                </el-form-item>
                <el-form-item label="地址" prop="address">
                    <el-input v-model="editAccount.address" clearable  style="width: 100%" />
                </el-form-item>
                <el-form-item label="账号" prop="username">
                    <el-input v-model="editAccount.username" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input v-model="editAccount.password" clearable type="password" style="width: 100%"
                        autocomplete="new-password" show-password />
                </el-form-item>
				<el-form-item label="状态" prop="status">
				    <el-select v-model="editAccount.status"  style="width: 100%">
				        <el-option v-for="status in statusOptions" :key="status.dictLabel" :label="status.dictLabel"
				            :value="status.dictValue" />
				    </el-select>
				</el-form-item>
                
                <el-form-item label=" 数据库地址" prop="dbUrl">
                    <el-input v-model="editAccount.dbUrl" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库端口" prop="dbPort">
                    <el-input v-model="editAccount.dbPort"  clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库用户名" prop="dbName">
                    <el-input v-model="editAccount.dbName" clearable style="width: 100%" autocomplete="off" />
                </el-form-item>
                <el-form-item label=" 数据库密码" prop="dbPassword">
                    <el-input v-model="editAccount.dbPassword" clearable style="width: 100%" autocomplete="new-password"
                        show-password />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelFn2">取消</el-button>
                    <el-button type="primary" @click="editAccountFn">编辑</el-button>
                </span>
            </template>
        </el-dialog>


        <!-- 修改密码对话框 -->
        <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
            <el-form :model="{ password: newPassword }" label-width="80px">
                <el-form-item label="新密码" required>
                    <el-input v-model="newPassword" type="password" autocomplete="new-password" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showPasswordDialog = false">取消</el-button>
                    <el-button type="primary" @click="updatePassword">确认</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 设置状态对话框 -->
        <el-dialog v-model="showStatusDialog" title="设置状态" width="400px">
            <el-form :model="{ status: selectedStatus }" label-width="80px">
                <el-form-item label="状态" required>
                    <el-select v-model="selectedStatus">
                        <el-option v-for="status in statusOptions" :key="status" :label="status" :value="status" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showStatusDialog = false">取消</el-button>
                    <el-button type="primary" @click="updateStatus">确认</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
    import { ref, reactive, onMounted } from 'vue'
    import { Plus } from '@element-plus/icons-vue'
    import { ElMessage, ElMessageBox } from 'element-plus';
    import { decryptsm4, encryptsm4 } from '@/utils/sm4Jsencrypt'
    import {
        getSandboxUserList,
        addSandboxUser,
        editSandboxUserStatus,
        editSandboxUserResetPassword,
        getSandboxList,
        getSandboxDataList,
        editSandboxFn,
        delSandboxFn
        // editPassword
    } from '@/api/systemManagement/sandboxAccountManagement'
    // 模拟数据 - 沙箱列表
    const sandboxes = ref([

    ])
    const showEditAccountDialog = ref(false)
    const newForm = ref()
    const searchQuery = ref('')
    // 模拟数据 - 账号列表
    const accounts = ref([])
    const editForm = ref()

    const rules = {
        sandboxName: [{ required: true, message: '请输入沙箱名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    };

    // 状态选项
    const statusOptions = ref([])

    // 对话框状态
    const showNewAccountDialog = ref(false)
    const showPasswordDialog = ref(false)
    const showStatusDialog = ref(false)
    const loading = ref(false)

    // 表单相关
    const newAccount = reactive({
        sandboxName: '',
        address: '',
        username: '',
        password: '',
        status: '',
        dbUrl: '',
        dbPort: '',
        dbName: '',
        dbPassword: ''
    })

    const editAccount = reactive({
        sandboxName: '',
        address: '',
        username: '',
        password: '',
        status: '',
        dbUrl: '',
        dbPort: '',
        dbName: '',
        dbPassword: '',
        id: ''
    })

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalItems = ref(0);



    const handleSizeChange = (val) => {
        pageSize.value = val;
        currentPage.value = 1;
        fetchAccountData()
    };

    const handleCurrentChange = (val) => {
        currentPage.value = val;
        fetchAccountData()
    };



    // 临时变量
    const editingAccount = ref(null)
    const newPassword = ref('')
    const selectedStatus = ref('')

    // 密码显示控制
    const showNewPassword = ref(false)
    const showEditPassword = ref(false)

    // 获取状态对应的类型
    const getStatusType = (status) => {
        const statusMap = {
            '可用': 'success',
            '临时禁用': 'warning',
            '已禁用': 'danger'
        }
        return statusMap[status] || 'info'
    }

    // 查看密码
    const showPassword = (account) => {
        account.showPassword = !account.showPassword
    }
    const showPassword2= (row) => {
      row.showPassword2 = !row.showPassword2;
    };
    // 修改密码
    const editPassword = (account) => {
        // editingAccount.value = account
        newPassword.value = ''
        showPasswordDialog.value = true
        updatePasswordId.value = account.id
    }

    const updatePasswordId = ref('')
    // 更新密码
    const updatePassword = () => {
        if (!newPassword.value) {
            ElMessage.warning('请输入新密码')
            return
        }
        editSandboxUserResetPassword({
            id: updatePasswordId.value,
            password: newPassword.value
        }).then(res => {
            if (res.code == 200) {
                newPassword.value = ''
                showPasswordDialog.value = false
                fetchAccountData()
                ElMessage.success('密码修改成功')
            }
            console.log(res)
        })


    }

    // 设置状态
    const toggleStatus = (account) => {
        editingAccount.value = account
        selectedStatus.value = account.status
        showStatusDialog.value = true
    }

    // 更新状态
    const updateStatus = () => {
        if (editingAccount.value) {
            editingAccount.value.status = selectedStatus.value
            showStatusDialog.value = false
            ElMessage.success('状态更新成功')
        }
    }

    const toggleRoleStatus = (row) => {
        const newStatus = row.status === 0 ? '禁用' : '启用';

        ElMessageBox.confirm(
            `确定要${newStatus}吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            editSandboxUserStatus({
                status: row.status == 0 ? 1 : 0,
                id: row.id
            }).then(res => {
                if (res.code == 200) {
                    fetchAccountData();
                    ElMessage.success(`当前状态更新成功`);
                }
            })


        }).catch(() => { });

    };

    const delRole = (row) => {
        ElMessageBox.confirm(
            `确定要删除吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            delSandboxFn({
                id: row.id
            }).then(res => {
                if (res.code == 200) {
                    fetchAccountData();
                    ElMessage.success(`删除成功!`);
                }
            })


        }).catch(() => { });
    }

    // 创建账号
    const createAccount = async () => {
        // || !newAccount.address || !newAccount.username || !newAccount.password
        await newForm.value.validate(async (valid) => {
            if (valid) {

                const params = {
                    sandboxName: newAccount.sandboxName,
                    userName: newAccount.username,
                    password: newAccount.password,
                    status: newAccount.status,
                    website: newAccount.address,
                    dbUrl: newAccount.dbUrl,
                    dbPort: newAccount.dbPort,
                    dbName: newAccount.dbName,
                    dbPassword: newAccount.dbPassword
                }

                addSandboxUser(params).then(res => {
                    if (res.code == 200) {
                        console.log(res)
                        showNewAccountDialog.value = false
                        newAccount.sandboxName = undefined
                        newAccount.username = undefined
                        newAccount.password = undefined
                        newAccount.status = undefined
                        newAccount.address = undefined
                        newAccount.dbUrl = undefined
                        newAccount.dbPort = undefined
                        newAccount.dbName = undefined
                        newAccount.dbPassword = undefined
                        ElMessage.success('账号创建成功')
                        fetchAccountData()
                    }
                })



            }
        });



    }

    const editAccountFn = async () => {
        await editForm.value.validate(async (valid) => {
            if (valid) {
                // let sandboxId = sandboxes.value?.find(item => item.sandboxName == editAccount.sandboxId)?.id
                let status_ = statusOptions.value?.find(item => item.dictLabel == editAccount.status)?.dictValue
                // console.log('sandboxId: ', sandboxes.value, editAccount.sandboxId, sandboxId)

                // return
                const params = {
                    sandboxName: editAccount.sandboxName,
                    userName: editAccount.username,
                    password: editAccount.password,
                    status: status_ ? status_ : editAccount.status,
                    website: editAccount.address,
                    dbUrl: editAccount.dbUrl,
                    dbPort: editAccount.dbPort,
                    dbName: editAccount.dbName,
                    dbPassword: editAccount.dbPassword,
                    id: editAccount.id
                }

                // console.log(sandboxId, params)

                editSandboxFn(params).then(res => {
                    if (res.code == 200) {
                        console.log(res)
                        showNewAccountDialog.value = false
                        editAccount.sandboxId = undefined
                        editAccount.username = undefined
                        editAccount.password = undefined
                        editAccount.status = undefined
                        editAccount.address = undefined
                        editAccount.dbUrl = undefined
                        editAccount.dbPort = undefined
                        editAccount.dbName = undefined
                        editAccount.dbPassword = undefined
                        ElMessage.success('账号修改成功')
                        fetchAccountData()
                        showEditAccountDialog.value = false
                    }
                })



            }
        });



    }


    // 获取账号数据
    const fetchAccountData = async () => {
        loading.value = true
        try {
            let params = {
                userName: searchQuery.value,
                pageNum: currentPage.value,
                pageSize: pageSize.value
            }

            getSandboxUserList(params).then(res => {
                if (res.code == 200) {
                    accounts.value = res.rows
                    totalItems.value = res.total
                    console.log(res, 'res')
                    loading.value = false
                }
            })
            console.log('获取账号数据...', params)
        } catch (err) { }
    }

    const applyFilters = () => {
        currentPage.value = 1;
        fetchAccountData()
    }

    // 编辑角色
    const editRole = (role) => {
        console.log('edit', role)
        showEditAccountDialog.value = true
        editAccount.id = role.id
        editAccount.sandboxName = role.sandboxName
        editAccount.username = role.userName
        editAccount.password = role.password
        editAccount.status = role.statusStr
        editAccount.address = role.website
        editAccount.dbUrl = role.dbUrl
        editAccount.dbPort = role.dbPort
        editAccount.dbName = role.dbName
        editAccount.dbPassword = role.dbPassword
        editAccount.id = role.id
        // getSandboxList().then(res => {
        //     if (res.code == 200) {
        //         console.log(res, 'getSandboxList')
        //         sandboxes.value = res.data
        //     }
        // })
        getSandboxDataList({
            dictType: 'sandbox_user_status'
        }).then(res => {
            if (res.code == 200) {
                statusOptions.value = res.data
                console.log(res, 'sandbox_user_status')
            }
        })
    };

    const addFn = () => {
        // getSandboxList().then(res => {
        //     if (res.code == 200) {
        //         console.log(res, 'getSandboxList')
        //         sandboxes.value = res.data
        //     }
        // })
        getSandboxDataList({
            dictType: 'sandbox_user_status'
        }).then(res => {
            if (res.code == 200) {
                statusOptions.value = res.data
                console.log(res, 'sandbox_user_status')
            }
        })
        showNewAccountDialog.value = true
    }
    const cancelFn = () => {
        newAccount.sandboxName = undefined
        newAccount.username = undefined
        newAccount.password = undefined
        newAccount.status = undefined
        newAccount.address = undefined
        newAccount.dbUrl = undefined
        newAccount.dbPort = undefined
        newAccount.dbName = undefined
        newAccount.dbPassword = undefined
        showNewAccountDialog.value = false
    }
    const cancelFn2 = () => {
        editAccount.sandboxName = undefined
        editAccount.username = undefined
        editAccount.password = undefined
        editAccount.status = undefined
        editAccount.address = undefined
        editAccount.dbUrl = undefined
        editAccount.dbPort = undefined
        editAccount.dbName = undefined
        editAccount.dbPassword = undefined
        showEditAccountDialog.value = false
    }
    onMounted(() => {
        fetchAccountData()
    })
</script>

<style scoped>
    .sandbox-account {
        padding: 20px;
    }

    .page-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #303133;
    }

    .operation-bar {
        margin-bottom: 20px;
    }

    .table-card {
        margin-bottom: 20px;
    }

    .password-mask {
        font-family: monospace;
        margin-right: 8px;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }


    .custom-pagination {
        --el-pagination-button-bg-color: #fff;
        --el-pagination-hover-color: #409eff;
        display: flex;
        justify-content: flex-end;
        padding: 16px 0;
    }
</style>