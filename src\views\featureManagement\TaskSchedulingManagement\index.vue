<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 加载状态
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  name: '',
  status: '',
  period: ''
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '每日企业数据更新任务',
    description: '从外部数据源更新企业基本信息',
    createTime: '2024-05-18 10:00:00',
    lastRunTime: '2024-05-20 08:00:00',
    nextRunTime: '2024-05-21 08:00:00',
    period: '每日',
    status: '正常'
  },
  {
    id: 2,
    name: '企业风险评分计算',
    description: '计算企业的信用风险评分',
    createTime: '2024-05-15 14:30:00',
    lastRunTime: '2024-05-20 09:00:00',
    nextRunTime: '2024-05-21 09:00:00',
    period: '每日',
    status: '正常'
  },
  {
    id: 3,
    name: '月度企业指标汇总',
    description: '统计企业月度经营指标',
    createTime: '2024-04-28 16:45:00',
    lastRunTime: '2024-04-30 23:00:00',
    nextRunTime: '2024-05-31 23:00:00',
    period: '每月',
    status: '正常'
  },
  {
    id: 4,
    name: '企业变更信息同步',
    description: '同步企业工商变更信息',
    createTime: '2024-05-10 09:15:00',
    lastRunTime: '2024-05-20 10:00:00',
    nextRunTime: '2024-05-21 10:00:00',
    period: '每日',
    status: '暂停'
  }
])

// 周期选项
const periodOptions = [
  { value: '', label: '全部' },
  { value: '每日', label: '每日' },
  { value: '每周', label: '每周' },
  { value: '每月', label: '每月' }
]

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '正常', label: '正常' },
  { value: '暂停', label: '暂停' },
  { value: '异常', label: '异常' }
]

// 查询
const handleSearch = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    const filteredData = tableData.value.filter(item => {
      const nameMatch = filterForm.name ? item.name.includes(filterForm.name) : true
      const statusMatch = filterForm.status ? item.status === filterForm.status : true
      const periodMatch = filterForm.period ? item.period === filterForm.period : true
      return nameMatch && statusMatch && periodMatch
    })
    tableData.value = filteredData
    loading.value = false
  }, 500)
}

// 重置筛选
const resetFilter = () => {
  filterForm.name = ''
  filterForm.status = ''
  filterForm.period = ''
}

// 配置任务
const configTask = (id) => {
  router.push(`/task-config/${id}`)
}

// 查看执行记录
const viewTaskHistory = (id) => {
  console.log('查看任务执行记录', id)
}

// 启停任务
const toggleTaskStatus = (id) => {
  const task = tableData.value.find(item => item.id === id)
  if (task) {
    task.status = task.status === '正常' ? '暂停' : '正常'
    ElMessage.success(`任务已${task.status === '正常' ? '启动' : '暂停'}`)
  }
}

// 添加新任务
const addNewTask = () => {
  router.push('/task-config/new')
}

onMounted(() => {
  // 页面加载时可以从API获取任务列表
})
</script>

<template>
  <div class="task-scheduling-container">
    <div class="page-header">
      <!-- <h1 class="page-title">任务调度管理</h1>
       -->
      <!-- 操作按钮 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="success" class="action-button" @click="addNewTask">
            <el-icon class="mr-2"><Plus /></el-icon>
            新建任务
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" label-width="80px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="任务名称">
              <el-input v-model="filterForm.name" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行周期">
              <el-select v-model="filterForm.period" placeholder="请选择执行周期" class="w-full">
                <el-option
                  v-for="option in periodOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="filterForm.status" placeholder="请选择状态" class="w-full">
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="filter-actions">
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 'bold'
        }"
        :row-style="{ height: '60px' }"
        border
      >
        <el-table-column prop="id" label="任务ID" width="80" align="center" />
        <el-table-column prop="name" label="任务名称" min-width="150" />
        <el-table-column prop="description" label="任务描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="lastRunTime" label="上次执行时间" width="160" />
        <el-table-column prop="nextRunTime" label="下次执行时间" width="160" />
        <el-table-column prop="period" label="执行周期" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === '正常' ? 'success' : row.status === '暂停' ? 'warning' : 'danger'"
              effect="light"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button link type="primary" @click="configTask(row.id)">配置</el-button>
              <el-button link type="primary" @click="viewTaskHistory(row.id)">执行记录</el-button>
              <el-button
                link
                :type="row.status === '正常' ? 'warning' : 'success'"
                @click="toggleTaskStatus(row.id)"
              >
                {{ row.status === '正常' ? '暂停' : '启动' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.task-scheduling-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24px;
}

.left-actions {
  display: flex;
  /* gap: 12px; */
  justify-content: flex-end;
}

.action-button {
  display: flex;
  align-items: center;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.filter-form {
  padding: 8px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.table-card {
  border-radius: 8px;
}

.table-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

:deep(.el-card) {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}

.w-full {
  width: 100%;
}

.mr-2 {
  margin-right: 8px;
}
</style> 