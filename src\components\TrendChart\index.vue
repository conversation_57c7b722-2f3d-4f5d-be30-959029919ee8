<template>
    <div ref="chartRef" :style="{ width: '100%', height: '320px' }"></div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
  import * as echarts from 'echarts'
  
  const props = defineProps({
    chartData: {
      type: Object,
      required: true
    }
  })
  
  const chartRef = ref(null)
  let chartInstance = null
  
  const renderChart = () => {
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value)
    }
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: props.chartData.x },
      yAxis: { type: 'value' },
      series: [
        {
          name: '处理数',
          type: 'line',
          data: props.chartData.y,
          smooth: true,
          areaStyle: {}
        }
      ]
    }
    chartInstance.setOption(option)
  }
  
  onMounted(() => {
    renderChart()
  })
  
  watch(() => props.chartData, () => {
    renderChart()
  }, { deep: true })
  
  onBeforeUnmount(() => {
    chartInstance && chartInstance.dispose()
  })
  </script>