import request from '@/utils/request'



// 列表
export function getVarDictList(data) {
  return request({
    url: '/biz/varDict/page',
    method: 'post',
    data: data
  })
}

//下拉列表
export function getDataList(data) {
  return request({
    url: '/system/dict/data/dataList',
    method: 'post',
    data: data,
   
  })
}

//下载
export function exportDown(data) {
  return request({
    url: '/biz/varDict/export',
    method: 'post',
    data: data,
     responseType: 'blob',
  })
}

//下载
export function exportDown_(data) {
  return request({
    url: '/biz/maintenance/export',
    method: 'post',
    data: data,
     responseType: 'blob',
  })
}

//数据权限配置查询 /system/role/roleVarDictPage

export function roleVarDictPage(data) {
  return request({
    url: '/system/role/roleVarDictPage',
    method: 'post',
    data: data,
  })
}



export function roleVarDictEdit(data) {
  return request({
    url: '/system/role/roleVarDictEdit',
    method: 'post',
    data: data,
  })
}


// /biz/varDict/edit


export function editFm(data) {
  return request({
    url: '/biz/varDict/edit',
    method: 'post',
    data: data,
  })
}


//一级维度选项
export function getfirstDimensionOptions(data) {
  return request({
    url: '/biz/varDict/firstDimensions',
    method: 'post',
    data: data,
  })
}

//二级维度选项
export function getsecondDimensionOptions(data) {
  return request({
    url: '/biz/varDict/secondDimensions',
    method: 'post',
    data: data,
  })
}
//三级维度选项
export function getthirdDimensionOptions(data) {
  return request({
    url: '/biz/varDict/thirdDimensions',
    method: 'post',
    data: data,
  })
}