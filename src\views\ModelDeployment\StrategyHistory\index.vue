<template>
  <div class="strategy-history">
    <h2 class="page-title">策略历史</h2>
    
    <!-- 筛选区域 -->
    <el-card class="filter-section">
      <el-form :model="filters" label-width="100px" class="filter-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="策略ID">
              <el-input v-model="filters.strategyId" placeholder="请输入策略ID" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="策略名称">
              <el-input v-model="filters.strategyName" placeholder="请输入策略名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属业务">
              <el-select v-model="filters.businessArea" placeholder="请选择" class="w-full">
                <el-option label="全部" value="" />
                <el-option v-for="option in businessAreaOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作类型">
              <el-select v-model="filters.operationType" placeholder="请选择" class="w-full">
                <el-option label="全部" value="" />
                <el-option v-for="option in operationTypeOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开始日期">
              <el-date-picker v-model="filters.startDate" type="date" placeholder="选择日期" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束日期">
              <el-date-picker v-model="filters.endDate" type="date" placeholder="选择日期" class="w-full" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="applyFilters">查询</el-button>
          <el-button type="success" @click="exportData">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-section">
      <el-table :data="paginatedHistory" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="operationTime" label="操作时间" />
        <el-table-column prop="strategyId" label="策略ID" />
        <el-table-column prop="strategyName" label="策略名称" />
        <el-table-column prop="businessArea" label="所属业务" />
        <el-table-column prop="version" label="版本" />
        <el-table-column prop="operationType" label="操作类型">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.operationType)">
              {{ row.operationType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewDetail(row)">查看</el-button>
            <el-button v-if="row.operationType === '发布'" type="primary" link @click="compareVersions(row)">版本对比</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 模拟数据
const historyRecords = ref([
  { id: 1, operationTime: '2024-05-20 10:30:00', strategyId: 'STRAT001', strategyName: '贷前准入策略V1.2', businessArea: '贷前审批', version: '1.2', operationType: '发布', operator: '张三' },
  { id: 2, operationTime: '2024-05-19 15:45:00', strategyId: 'STRAT001', strategyName: '贷前准入策略V1.2', businessArea: '贷前审批', version: '1.1', operationType: '修改', operator: '张三' },
  { id: 3, operationTime: '2024-05-18 09:20:00', strategyId: 'STRAT001', strategyName: '贷前准入策略V1.0', businessArea: '贷前审批', version: '1.0', operationType: '创建', operator: '张三' },
  { id: 4, operationTime: '2024-05-17 14:10:00', strategyId: 'STRAT002', strategyName: '贷中风险监控策略A', businessArea: '贷中监控', version: '1.0', operationType: '创建', operator: '李四' },
  { id: 5, operationTime: '2024-05-16 11:30:00', strategyId: 'STRAT003', strategyName: '反欺诈基础规则', businessArea: '反欺诈', version: '0.9', operationType: '创建', operator: '王五' },
  { id: 6, operationTime: '2024-05-15 16:25:00', strategyId: 'STRAT004', strategyName: '早期催收策略', businessArea: '贷后催收', version: '1.1', operationType: '修改', operator: '赵六' },
  { id: 7, operationTime: '2024-05-14 10:45:00', strategyId: 'STRAT004', strategyName: '早期催收策略', businessArea: '贷后催收', version: '1.0', operationType: '创建', operator: '赵六' },
  { id: 8, operationTime: '2024-05-13 09:15:00', strategyId: 'STRAT002', strategyName: '贷中风险监控策略A', businessArea: '贷中监控', version: '1.0', operationType: '发布', operator: '李四' },
  { id: 9, operationTime: '2024-05-12 14:30:00', strategyId: 'STRAT005', strategyName: '贷前准入策略V1.3-候选', businessArea: '贷前审批', version: '1.3', operationType: '创建', operator: '张三' },
  { id: 10, operationTime: '2024-05-11 11:20:00', strategyId: 'STRAT004', strategyName: '早期催收策略', businessArea: '贷后催收', version: '1.1', operationType: '停用', operator: '刘经理' },
  { id: 11, operationTime: '2024-05-10 09:00:00', strategyId: 'STRAT006', strategyName: '贷中大额交易监控', businessArea: '贷中监控', version: '1.0', operationType: '创建', operator: '李四' },
  { id: 12, operationTime: '2024-05-09 16:15:00', strategyId: 'STRAT007', strategyName: '信用卡申请反欺诈', businessArea: '反欺诈', version: '1.0', operationType: '创建', operator: '王五' },
  { id: 13, operationTime: '2024-05-08 10:30:00', strategyId: 'STRAT003', strategyName: '反欺诈基础规则', businessArea: '反欺诈', version: '0.9', operationType: '修改', operator: '王五' },
  { id: 14, operationTime: '2024-05-07 14:40:00', strategyId: 'STRAT008', strategyName: '小额信贷催收策略', businessArea: '贷后催收', version: '1.0', operationType: '创建', operator: '赵六' },
  { id: 15, operationTime: '2024-05-06 11:10:00', strategyId: 'STRAT003', strategyName: '反欺诈基础规则', businessArea: '反欺诈', version: '0.8', operationType: '创建', operator: '王五' },
]);

// 筛选条件
const filters = reactive({
  strategyId: '',
  strategyName: '',
  businessArea: '',
  operationType: '',
  startDate: '',
  endDate: ''
});

// 业务领域选项
const businessAreaOptions = ['贷前审批', '贷中监控', '贷后催收', '反欺诈', '营销'];

// 操作类型选项
const operationTypeOptions = ['创建', '修改', '发布', '停用', '删除'];

// 获取操作类型对应的标签类型
const getOperationTypeTag = (type) => {
  const typeMap = {
    '创建': 'info',
    '修改': 'warning',
    '发布': 'success',
    '停用': 'danger',
    '删除': 'danger'
  };
  return typeMap[type] || 'info';
};

// 应用筛选
const applyFilters = () => {
  currentPage.value = 1;
};

// 重置筛选
const resetFilters = () => {
  filters.strategyId = '';
  filters.strategyName = '';
  filters.businessArea = '';
  filters.operationType = '';
  filters.startDate = '';
  filters.endDate = '';
  currentPage.value = 1;
};

// 导出数据
const exportData = () => {
  ElMessage.info('导出数据功能待实现');
};

// 查看详情
const viewDetail = (record) => {
  ElMessage.info(`查看策略历史详情: ${record.strategyName} (版本: ${record.version})\n操作类型: ${record.operationType}\n操作时间: ${record.operationTime}\n操作人: ${record.operator}`);
};

// 版本对比
const compareVersions = (record) => {
  ElMessage.info(`对比版本: ${record.strategyName} - 版本 ${record.version}\n该功能待实现`);
};

// 过滤历史记录
const filteredHistory = computed(() => {
  return historyRecords.value.filter(record => {
    const strategyIdMatch = filters.strategyId ? record.strategyId.toLowerCase().includes(filters.strategyId.toLowerCase()) : true;
    const strategyNameMatch = filters.strategyName ? record.strategyName.toLowerCase().includes(filters.strategyName.toLowerCase()) : true;
    const businessAreaMatch = filters.businessArea ? record.businessArea === filters.businessArea : true;
    const operationTypeMatch = filters.operationType ? record.operationType === filters.operationType : true;
    
    let startDateMatch = true;
    let endDateMatch = true;
    
    if (filters.startDate) {
      const recordDate = new Date(record.operationTime);
      const filterStartDate = new Date(filters.startDate);
      startDateMatch = recordDate >= filterStartDate;
    }
    
    if (filters.endDate) {
      const recordDate = new Date(record.operationTime);
      const filterEndDate = new Date(filters.endDate);
      filterEndDate.setHours(23, 59, 59, 999);
      endDateMatch = recordDate <= filterEndDate;
    }
    
    return strategyIdMatch && strategyNameMatch && businessAreaMatch && operationTypeMatch && startDateMatch && endDateMatch;
  }).sort((a, b) => new Date(b.operationTime) - new Date(a.operationTime));
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => filteredHistory.value.length);

const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredHistory.value.slice(start, end);
});

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 获取数据
const fetchHistoryData = async () => {
  console.log('获取策略历史数据...');
  // 在实际应用中应调用API
  // historyRecords.value = await api.getStrategyHistory();
};

onMounted(() => {
  fetchHistoryData();
});
</script>

<style scoped>
.strategy-history {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 48px);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1f2937;
  position: relative;

}



.filter-section {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.filter-section :deep(.el-card__body) {
  padding: 24px;
}

.filter-form {
  margin-top: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.filter-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.filter-form :deep(.el-input__wrapper),
.filter-form :deep(.el-select) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.filter-form :deep(.el-input__wrapper:hover),
.filter-form :deep(.el-select:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.filter-form :deep(.el-input__wrapper.is-focus),
.filter-form :deep(.el-select.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.table-section {
  margin-top: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-section :deep(.el-card__body) {
  padding: 24px;
}

.table-section :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.table-section :deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #606266;
  height: 48px;
}

.table-section :deep(.el-table td) {
  padding: 12px 0;
}

.table-section :deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

.table-section :deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

.table-section :deep(.el-button--link) {
  padding: 4px 8px;
  font-weight: 500;
}

.pagination-container {
  margin-top: 24px;
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
}

.pagination-container :deep(.el-pagination) {
  padding: 0;
}

.pagination-container :deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

.pagination-container :deep(.el-pagination .el-pagination__sizes) {
  margin-right: 16px;
}

.w-full {
  width: 100%;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
}

:deep(.el-button) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: #66b1ff;
  --el-button-hover-border-color: #66b1ff;
}

:deep(.el-button--success) {
  --el-button-hover-bg-color: #85ce61;
  --el-button-hover-border-color: #85ce61;
}
</style> 