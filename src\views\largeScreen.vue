<template>
  <div class="largeScreen">
    <!-- 背景 -->
    <div class="background">
      <div class="network-topology"></div>
      <div class="dynamic-network" ref="dynamicNetwork"></div>
    </div>

    <div class="container">
      <!-- Logo -->
      <div class="logo">
        <img src="../assets/logo/logo2.png" alt="Logo" />
      </div>
      <!-- 标题 -->
      <header class="header">
        <h3 class="title">企业信用风险决策特征平台</h3>
        <p class="subtitle">CREDIT DECISION PLATFORM DASHBOARD</p>
      </header>

      <!-- 平台特色介绍 -->
      <section class="features">
        <el-row :gutter="15" style="width: 100%;">
          <el-col :span="8" v-for="(feature, i) in features" :key="i">
            <el-card class="feature-card" shadow="hover">
              <h4 class="feature-title-sub" style="color: white">{{ feature.sub }}</h4>
              <h3 class="feature-title" style="color: white">{{ feature.title }}</h3>
              <p class="feature-desc">{{ feature.desc }}</p>
            </el-card>
          </el-col>
        </el-row>
      </section>

      <!-- 主要功能 -->
      <section class="main-functions">
        <div v-for="(func, i) in mainFunctions" :key="i">
          <el-card class="function-card" shadow="hover">
            <div class="function-icon">{{ func.icon }}</div>
            <h2 class="function-title">{{ func.title }}</h2>
            <p class="function-desc">{{ func.desc }}</p>
          </el-card>
        </div>
      </section>

      <!-- 特征介绍 -->
      <section class="characteristics">
        <!-- <div class="characteristics-title">特征介绍</div> -->
        <div class="characteristics-container">
          <div class="characteristics-grid">
            <div v-for="(item, i) in characteristics" :key="i" class="characteristic-wrapper">
              <el-card class="characteristic-item" shadow="hover">
                <h3 class="characteristic-name">{{ item.name }}</h3>
                <div class="characteristic-stats">
                  <div class="stat-item">
                    覆盖变量: <span class="stat-value">{{ item.value }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </section>

      <!-- 底部功能 -->
      <section class="bottom-functions"
        v-if="routeObj?.variableDictionary || routeObj.sampleMatching || routeObj.sandboxAccount">
        <div v-for="(btn, i) in bottomFunctions" :key="i">
          <template v-if="i == 0 && routeObj.shuoming">
            <el-button class="bottom-function" type="primary" round @click="goclcik()">
              <span class="bottom-function-icon">{{ btn.icon }}</span>
              <span class="bottom-function-name">{{ btn.name }}</span>
            </el-button>
          </template>
          <template v-if="i == 1 && routeObj.variableDictionary">
            <el-button class="bottom-function" type="primary" round @click="clcik(btn.path)">
              <span class="bottom-function-icon">{{ btn.icon }}</span>
              <span class="bottom-function-name">{{ btn.name }}</span>
            </el-button>
          </template>
          <template v-if="i == 2 && routeObj.sampleMatching">
            <el-button class="bottom-function" type="primary" round @click="clcik(btn.path)">
              <span class="bottom-function-icon">{{ btn.icon }}</span>
              <span class="bottom-function-name">{{ btn.name }}</span>
            </el-button>
          </template>
          <template v-if="i == 3 && routeObj.sandboxAccount">
            <el-button class="bottom-function" type="primary" round @click="clcik(btn.path)">
              <span class="bottom-function-icon">{{ btn.icon }}</span>
              <span class="bottom-function-name">{{ btn.name }}</span>
            </el-button>
          </template>
        </div>

      </section>
    </div>


    <!-- 操作说明模态弹框 -->
    <div v-if="instructionModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">操作说明</h2>
          <span class="close" id="closeModal" @click="closekuang">&times;</span>
        </div>
        <div class="modal-body">
          <div class="welcome-text">
            欢迎使用企业信用风险决策特征平台，请根据下述指引进行操作。
          </div>

          <div class="instruction-section">
            <h3 class="instruction-title">一、变量筛选</h3>
            <ul class="instruction-list">
              <li class="instruction-item">
                <span class="instruction-number">1.</span>设置筛选条件，点击"查询"筛选变量
              </li>
              <li class="instruction-item">
                <span class="instruction-number">2.</span>点击"查看"按钮获取单个变量详情
              </li>
              <li class="instruction-item">
                <span class="instruction-number">3.</span>点击"下载"导出筛选结果（默认导出全部变量）
              </li>
            </ul>
          </div>

          <div class="instruction-section">
            <h3 class="instruction-title">二、样本匹配</h3>
            <ul class="instruction-list">
              <li class="instruction-item">
                <span class="instruction-number">1.</span>点击"导入已选变量"上传建模需要的变量
              </li>
              <li class="instruction-item">
                <span class="instruction-number">2.</span>下载模板，填写统一社会信用代码和回溯日期
              </li>
              <li class="instruction-item">
                <span class="instruction-number">3.</span>上传填写好的模板文件
              </li>
              <li class="instruction-item">
                <span class="instruction-number">4.</span>输入自定义表名，用于后续建模使用
              </li>
              <li class="instruction-item">
                <span class="instruction-number">5.</span>点击"开始匹配"，进行样本回溯并建表
              </li>
              <li class="instruction-item">
                <span class="instruction-number">6.</span>点击"刷新"查看建表进度和匹配结果（匹配数量不一致属正常）
              </li>
            </ul>
          </div>

          <div class="instruction-section">
            <h3 class="instruction-title">三、沙箱建模</h3>
            <ul class="instruction-list">
              <li class="instruction-item">
                登录沙箱账号，获取建模平台和数据库连接信息，开展建模分析
              </li>
            </ul>
          </div>


        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    getrouterData
  } from "@/api/login";
  const instructionModal = ref(false)
  const features = [
    { sub: '特征变量', title: '10000+', desc: '覆盖政务数据、产业数据、金融数据' },
    { sub: '企业覆盖', title: '430万', desc: '覆盖深圳市所有商事主体' },
    { sub: '追溯时间', title: '9年', desc: '按月更新，最多可追溯9年数据' }
  ];

  const mainFunctions = [
    {
      icon: '👥',
      title: '客群分析',
      desc: '覆盖全市商事主体，精细化企业特征，支持银行构建量化KYC客群分析体系，赋能行业研究、中观分析和政策制定'
    },
    {
      icon: '🎯',
      title: '客户筛选',
      desc: '基于准入模型+响应模型+风险模型等智能算法，快速定位目标客户。提供灵活、实时、批量营销白名单工具+立体营销渠道，主动触达客户'
    },
    {
      icon: '⚖️',
      title: '信贷决策',
      desc: '提供开放式模型训练环境+领先机器学习算法工具，支持自主定制征信产品用于贷前、贷中、贷后环节'
    }
  ];

  const characteristics = [
    { name: '基本信息', value: '1000+' },
    { name: '资质创新', value: '1000+' },
    { name: '人力画像', value: '1000+' },
    { name: '资产状况', value: '500+' },
    { name: '经营成本', value: '2000+' },
    { name: '经营行为', value: '2000+' },
    { name: '经营成果', value: '500+' },
    { name: '经营风险', value: '1000+' },
    { name: '融资行为', value: '2000+' }
  ];

  const routeObj = reactive({
    shuoming: true,//操作说明
    sampleMatching: false,  //样本匹配
    sandboxAccount: false,    //沙箱账号
    variableDictionary: false //变量字典
  })


  const bottomFunctions = ref([
    { icon: '📖', name: '操作说明', path: '', flag: true },
    { icon: '🔍', name: '变量筛选', path: '/featureSelection/variableDictionary', sandboxAccount: true },
    { icon: '🎯', name: '样本匹配', path: '/featureSelection/sampleMatching', sampleMatching: true },
    { icon: '🧪', name: '沙箱环境', path: '/sandboxModel/sandboxAccount', sandboxAccount: true }
  ]);
  const router = useRouter();
  //操作说明
  const goclcik = () => {
    instructionModal.value = true
  }
  //关闭操作说明
  const closekuang = () => {
    instructionModal.value = false
  }
  const clcik = (path) => {
    router.push({ path: path });
  }

  // 动态网络图相关
  const featureFields = [
    { name: '社保缴纳', size: 50, x: 41, y: 28 },
    { name: '公积金', size: 50, x: 25, y: 25 },
    { name: '用气', size: 50, x: 15, y: 20 },
    { name: '用水', size: 50, x: 10, y: 50 },
    { name: '经营地址', size: 50, x: 20, y: 30 },
    { name: '社保欠缴', size: 50, x: 60, y: 25 },
    { name: '水费欠缴', size: 50, x: 35, y: 35 },
    { name: '客户关系', size: 50, x: 50, y: 50 },
    { name: '人力画像', size: 50, x: 65, y: 15 },
    { name: '技术创新', size: 50, x: 80, y: 40 },
    { name: '国际化水平', size: 50, x: 42, y: 40 },
    { name: '专利技术', size: 55, x: 90, y: 50 },
    { name: '成长能力', size: 50, x: 75, y: 30 },
    { name: '媒体声誉', size: 50, x: 85, y: 25 },
    { name: '安全生产', size: 50, x: 90, y: 20 },
    { name: '员工满意', size: 50, x: 70, y: 15 },
    { name: '行业地位', size: 50, x: 40, y: 15 },
    { name: '专利记录', size: 50, x: 30, y: 20 },
    { name: '供应链', size: 50, x: 56, y: 35 },
    { name: '信用记录', size: 50, x: 85, y: 50 },
    { name: '基本信息', size: 50, x: 55, y: 60 },
    { name: '市场份额', size: 50, x: 12, y: 35 },
    { name: '数字化程度', size: 50, x: 88, y: 40 },
    { name: '政府关系', size: 50, x: 18, y: 55 },
    { name: '经营行为', size: 50, x: 32, y: 45 },
    { name: '环境责任', size: 50, x: 35, y: 35 },
    { name: '社会贡献', size: 50, x: 65, y: 35 },
    { name: '公司治理', size: 50, x: 50, y: 30 },
    { name: '产品质量', size: 50, x: 35, y: 58 },
    { name: '创新能力', size: 50, x: 80, y: 58 },
    { name: '融资能力', size: 50, x: 40, y: 55 },
    { name: '盈利能力', size: 50, x: 60, y: 55 },
    { name: '品牌影响', size: 50, x: 22, y: 12 },
    { name: '市场策略', size: 50, x: 78, y: 12 },
    { name: '风险管理', size: 50, x: 50, y: 15 }
  ];

  const dynamicNetwork = ref(null);
  let networkNodes = [];
  let networkConnections = [];
  let intervalId = null;

  function createDynamicNetwork() {
    const networkContainer = dynamicNetwork.value;
    if (!networkContainer) return;
    const containerWidth = window.innerWidth;
    const containerHeight = window.innerHeight;

    networkContainer.innerHTML = '';
    networkNodes = [];
    networkConnections = [];

    featureFields.forEach((field) => {
      const node = document.createElement('div');
      node.className = 'network-node';
      if (field.name.length === 4) {
        node.innerHTML = field.name.substring(0, 2) + '<br>' + field.name.substring(2);
      } else {
        node.textContent = field.name;
      }
      node.style.width = field.size + 'px';
      node.style.height = field.size + 'px';
      const x = (field.x / 100) * containerWidth;
      const y = (field.y / 100) * containerHeight;
      node.style.left = x + 'px';
      node.style.top = y + 'px';
      networkContainer.appendChild(node);
      networkNodes.push({ element: node, x: x + field.size / 2, y: y + field.size / 2, field });
    });

    createConnections();
  }


  getrouterData().then(res => {
    console.log(res, 'getrouterData')
    if (res.code == 200) {
      routeObj.variableDictionary = res.data.variableDictionary
      routeObj.sampleMatching = res.data.sampleMatching
      routeObj.sandboxAccount = res.data.sandboxAccount

      console.log('bottomFunctions:', bottomFunctions.value)
    } else {
      ElMessage.warning(res.msg)
    }
  })

  function createConnections() {
    const networkContainer = dynamicNetwork.value;
    const nodeConnectionsCount = new Array(networkNodes.length).fill(0);

    const addConnection = (node1, node2) => {
      const distance = Math.sqrt(Math.pow(node1.x - node2.x, 2) + Math.pow(node1.y - node2.y, 2));
      const connection = document.createElement('div');
      connection.className = 'network-connection';
      const angle = Math.atan2(node2.y - node1.y, node2.x - node1.x) * 180 / Math.PI;
      connection.style.left = node1.x + 'px';
      connection.style.top = node1.y + 'px';
      connection.style.width = distance + 'px';
      connection.style.transform = `rotate(${angle}deg)`;
      networkContainer.appendChild(connection);
      networkConnections.push({ element: connection, node1, node2 });
      const node1Index = networkNodes.indexOf(node1);
      const node2Index = networkNodes.indexOf(node2);
      if (node1Index !== -1) nodeConnectionsCount[node1Index]++;
      if (node2Index !== -1) nodeConnectionsCount[node2Index]++;
    }

    const connectionExists = (n1, n2) => {
      return networkConnections.some(
        (conn) => (conn.node1 === n1 && conn.node2 === n2) || (conn.node1 === n2 && conn.node2 === n1)
      );
    };

    networkNodes.forEach((node1, i) => {
      const sortedNodes = networkNodes
        .filter((n) => n !== node1)
        .map((node2) => ({
          node: node2,
          distance: Math.sqrt(Math.pow(node1.x - node2.x, 2) + Math.pow(node1.y - node2.y, 2))
        }))
        .sort((a, b) => a.distance - b.distance);

      let connectionsMade = nodeConnectionsCount[i];
      for (let k = 0; k < sortedNodes.length && connectionsMade < 3; k++) {
        const node2 = sortedNodes[k].node;
        if (!connectionExists(node1, node2)) {
          addConnection(node1, node2);
          connectionsMade++;
        }
      }
    });
  }

  function randomActivateNodes() {
    networkNodes.forEach((node) => node.element.classList.remove('active'));
    networkConnections.forEach((conn) => conn.element.classList.remove('active'));

    const activeCount = Math.floor(Math.random() * 5) + 2;
    const activeNodes = [];
    for (let i = 0; i < activeCount && i < networkNodes.length; i++) {
      const randomIndex = Math.floor(Math.random() * networkNodes.length);
      const node = networkNodes[randomIndex];
      if (!activeNodes.includes(node)) {
        node.element.classList.add('active');
        activeNodes.push(node);
      }
    }
    networkConnections.forEach((conn) => {
      if (activeNodes.includes(conn.node1) && activeNodes.includes(conn.node2)) {
        conn.element.classList.add('active');
      }
    });
  }

  function handleResize() {
    createDynamicNetwork();
  }

  onMounted(() => {
    createDynamicNetwork();
    intervalId = setInterval(randomActivateNodes, 3000);
    window.addEventListener('resize', handleResize);
  });

  onBeforeUnmount(() => {
    clearInterval(intervalId);
    window.removeEventListener('resize', handleResize);
  });
</script>

<style>
  /* body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #0a0e27;
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        } */

  /* 网络拓扑背景 */
  .largeScreen .background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(circle at 50% 50%, #1a2456 0%, #0a0e27 70%);
  }

  .largeScreen .network-topology {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.6;
    background-image: url('data:image/svg+xml;base64,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<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');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    animation: networkPulse 8s ease-in-out infinite;

  }

  .largeScreen .el-card__body {
    padding: 0px 8px !important;
  }

  @keyframes networkPulse {

    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }

    50% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }

  /* 动态网络连接图 */
  .largeScreen .dynamic-network {
    position: absolute;
    top: 120px;
    /* 上移 */
    left: 0;
    width: 100%;
    height: calc(100% - 300px);
    /* 区域变高，间接变宽，因为节点会重新分布 */
    /* overflow: hidden; */
    pointer-events: none;
    z-index: 1;
  }

  .largeScreen .network-node {
    position: absolute;
    border-radius: 50%;
    background: rgba(20, 30, 80, 0.7);
    /* 深蓝色背景，半透明 */
    border: 1px solid rgba(59, 130, 246, 0.6);
    /* 蓝色边框 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    /* 增大字体 */
    color: #c0d8ff;
    /* 浅蓝色字体 */
    font-weight: normal;
    /* 正常字重 */
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(3px);
    /* 轻微模糊效果 */
    padding: 10px;
    /* 增加内边距 */
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
    /* 调整阴影 */
    /* word-break: break-all; /* Reverted */
    overflow-wrap: break-word;
    /* More standard property for word breaking */
    line-height: 1.2;
    /* 调整行高以适应换行 */
    /* Ensure width and height are set by JS to maintain circle */
  }

  .largeScreen .network-node.active {
    background: rgba(30, 50, 120, 0.9);
    /* 激活时更亮的背景 */
    border-color: rgba(80, 160, 255, 0.9);
    /* 激活时更亮的边框 */
    box-shadow: 0 0 20px rgba(80, 160, 255, 0.5);
    /* 激活时更强的阴影 */
    transform: scale(1.1);
    /* 激活时放大效果 */
    color: #ffffff;
    /* 激活时白色字体 */
  }

  .largeScreen .network-connection {
    position: absolute;
    height: 1px;
    /* 连接线细一些 */
    background: rgba(59, 130, 246, 0.4);
    /* 蓝色连接线，半透明 */
    opacity: 0.5;
    transform-origin: 0 0;
    transition: all 0.3s ease;
    /* 移除渐变和阴影，使其更简洁 */
  }

  .largeScreen .network-connection.active {
    opacity: 0.9;
    height: 1.5px;
    /* 激活时略粗 */
    background: rgba(80, 160, 255, 0.8);
    /* 激活时更亮的蓝色 */
  }

  @keyframes nodeGlow {

    0%,
    100% {
      opacity: 0.6;
    }

    50% {
      opacity: 1;
    }
  }

  /* Logo 样式 */
  .largeScreen .logo {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 150px;
    height: 40px;
    z-index: 2;
    opacity: 0.9;
  }

  .largeScreen .logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .largeScreen .container {
    position: relative;
    z-index: 1;
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 15px 15px 15px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  /* 标题 */
  .largeScreen .header {
    text-align: center;
    margin-bottom: 10px;
    flex-shrink: 0;
  }

  .largeScreen .title {
    font-size: 2.8rem;
    font-weight: bold;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }

  .largeScreen .subtitle {
    font-size: 1rem;
    color: #94a3b8;
    letter-spacing: 2px;
  }

  /* 特色介绍 */
  .largeScreen .features {
    display: flex;
    width: 100%;
    margin-bottom: 0px;
    margin-top: 0;
    flex-shrink: 0;
  }

  .largeScreen .feature-card {
    flex: 1;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }

  .largeScreen .feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.6);
  }

  .largeScreen .feature-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
    color: #3b82f6;
  }

  .largeScreen .feature-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 6px;
  }

  .largeScreen .feature-title-sub {
    font-size: 0.8rem;
    font-weight: bold;
    margin-bottom: 6px;
  }

  .largeScreen .feature-desc {
    color: #94a3b8;
    font-size: 0.8rem;
  }

  /* 主要功能 */
  .largeScreen .main-functions {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 240px;
    flex-shrink: 0;
    position: relative;
    margin: auto;
    height: 380px;
  }

  .largeScreen .function-card {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    padding: 15px 12px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    width: 200px;
    height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .largeScreen .function-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  .largeScreen .function-card:hover::before {
    left: 100%;
  }

  .largeScreen .function-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
  }

  .largeScreen .function-icon {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #3b82f6;
  }

  .largeScreen .function-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 8px;
    color: #ffffff;
  }

  .largeScreen .function-desc {
    color: #cbd5e1;
    line-height: 1.4;
    font-size: 0.75rem;
  }

  /* 特征介绍 */
  .largeScreen .characteristics {
    margin-bottom: 100px;
    flex-shrink: 0;
  }

  .largeScreen .characteristics-title {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #3b82f6;
  }

  .largeScreen .characteristics-container {
    overflow: hidden;
  }

  .largeScreen .characteristics-grid {
    display: flex;
    justify-content: space-between;
    /* grid-template-columns: repeat(9, 1fr); */
    /* gap: 10px; */
    padding: 0;
  }

  .largeScreen .characteristic-wrapper {
    flex: 1;
    display: flex;
  }

  .largeScreen .characteristic-item {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    flex: 1;
  }

  .largeScreen .characteristic-item:hover {
    /* transform: translateY(-2px); */
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  }

  .largeScreen .characteristic-name {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 8px;
    color: #ffffff !important;
  }

  .largeScreen .characteristic-stats {
    color: #94a3b8;
    font-size: 0.9rem;
  }

  .largeScreen .stat-item {
    margin-bottom: 3px;
  }

  .largeScreen .stat-value {
    color: #3b82f6;
    font-weight: bold;
  }

  /* 底部功能按钮 */
  .largeScreen .bottom-functions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    /* gap: 15px; */
    z-index: 1000;
    background: rgba(10, 14, 39, 0.9);
    padding: 15px 20px;
    border-radius: 50px;
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(20px);
  }

  .largeScreen .bottom-function {



    border-color: rgba(59, 130, 246, 0.8);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3));
    border-radius: 25px;
    padding: 12px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
    margin: 0 5px;
  }

  .largeScreen .bottom-function:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 232, 180, 0.27);

    background: rgba(50, 37, 7, 0.51);
    border: 1px solid rgba(255, 232, 180, 0.27);
  }

  .largeScreen .bottom-function-icon {
    font-size: 1.2rem;
    color: #3b82f6;
  }

  .largeScreen .bottom-function-name {
    font-size: 0.9rem;
    font-weight: bold;
    color: #ffffff;
  }

  /* 滚动条样式 */
  .characteristics-container::-webkit-scrollbar {
    height: 8px;
  }

  .characteristics-container::-webkit-scrollbar-track {
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
  }

  .characteristics-container::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 4px;
  }

  .characteristics-container::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
  }

  /* 模态弹框样式 */
  .modal {
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
  }

  .modal-content {
    background: linear-gradient(135deg, rgba(10, 14, 39, 0.95), rgba(26, 36, 86, 0.95));
    margin: 3% auto;
    padding: 0;
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 15px;
    width: 80%;
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(20px);
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-50px) scale(0.9);
    }

    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .modal-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
    padding: 20px 30px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
    margin: 0;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .close {
    color: #94a3b8;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
  }

  .close:hover,
  .close:focus {
    color: #ffffff;
    background: rgba(59, 130, 246, 0.3);
    transform: scale(1.1);
  }

  .modal-body {
    padding: 30px;
    color: #ffffff;
    line-height: 1.6;
  }

  .instruction-section {
    margin-bottom: 25px;
  }

  .instruction-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #3b82f6;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(59, 130, 246, 0.3);
  }

  .instruction-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .instruction-item {
    margin-bottom: 12px;
    padding: 12px 15px;
    background: rgba(59, 130, 246, 0.05);
    border-left: 3px solid #3b82f6;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
  }

  .instruction-item:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateX(5px);
  }

  .instruction-number {
    color: #8b5cf6;
    font-weight: bold;
    margin-right: 8px;
  }

  .welcome-text {
    font-size: 1.1rem;
    color: #94a3b8;
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }


  /* 响应式设计 */
  @media (max-width: 1200px) {
    .main-functions {
      gap: 40px;
    }

    .function-card {
      width: 240px;
      height: 320px;
    }

    .characteristics {
      margin-bottom: 60px;
    }

    .characteristics-container {
      overflow-x: auto;
      overflow-y: hidden;
      padding-bottom: 10px;
    }

    .characteristics-grid {
      display: flex;
      flex-wrap: nowrap;
      gap: 8px;
      min-width: max-content;
    }

    .characteristic-wrapper {
      flex-shrink: 0;
    }

    .characteristic-item {
      min-width: 120px;
      flex-shrink: 0;
    }

    .characteristic-name {
      font-size: 0.9rem !important;
    }

    .characteristic-stats {
      font-size: 0.8rem !important;
    }
  }

  @media (max-width: 768px) {
    .title {
      font-size: 2rem;
    }

    .main-functions {
      flex-direction: column;
      gap: 20px;
    }

    .function-card {
      width: 100%;
      height: 200px;
      max-width: 300px;
    }

    .characteristics {
      margin-bottom: 40px;
      padding: 0 10px;
    }

    .characteristics-container {
      overflow-x: auto;
      overflow-y: hidden;
      padding-bottom: 15px;
      margin: 0 -10px;
      padding-left: 10px;
      padding-right: 10px;
    }

    .characteristics-grid {
      display: flex;
      flex-wrap: nowrap;
      gap: 10px;
      min-width: max-content;
      padding: 0;
    }

    .characteristic-wrapper {
      flex-shrink: 0;
    }

    .characteristic-item {
      min-width: 100px;
      max-width: 120px;
      flex-shrink: 0;
      padding: 8px 6px !important;
      width: 100%;
    }

    .characteristic-name {
      font-size: 0.8rem !important;
      margin-bottom: 6px !important;
      line-height: 1.2;
    }

    .characteristic-stats {
      font-size: 0.7rem !important;
    }

    .stat-item {
      margin-bottom: 2px !important;
    }

    .bottom-functions {
      flex-wrap: wrap;
      gap: 8px;
      padding: 10px 15px;
      bottom: 10px;
    }

    .bottom-function {
      min-width: 80px;
      padding: 8px 12px;
      font-size: 0.8rem;
      margin: 0 2px;
    }

    .bottom-function-icon {
      font-size: 1rem !important;
    }

    .bottom-function-name {
      font-size: 0.8rem !important;
    }
  }

  /* 超小屏幕适配 */
  @media (max-width: 480px) {
    .characteristics {
      margin-bottom: 30px;
      padding: 0 5px;
    }

    .characteristics-container {
      margin: 0 -5px;
      padding-left: 5px;
      padding-right: 5px;
    }

    .characteristics-grid {
      gap: 8px;
    }

    .characteristic-item {
      min-width: 90px;
      max-width: 100px;
      padding: 6px 4px !important;
    }

    .characteristic-name {
      font-size: 0.75rem !important;
      margin-bottom: 4px !important;
    }

    .characteristic-stats {
      font-size: 0.65rem !important;
    }

    .bottom-functions {
      padding: 8px 10px;
      gap: 6px;
    }

    .bottom-function {
      min-width: 70px;
      padding: 6px 10px;
      flex-direction: column;
      gap: 4px;
    }

    .bottom-function-icon {
      font-size: 0.9rem !important;
    }

    .bottom-function-name {
      font-size: 0.7rem !important;
    }
  }
</style>