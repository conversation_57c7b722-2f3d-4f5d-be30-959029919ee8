<template>
  <div class="sandbox-account-container">
    <div class="page-header">
      <h1 class="page-title">沙箱账号</h1>
      <!-- <div class="action-buttons">
        
      </div> -->
    </div>
    
    <!-- 账号信息卡片 -->
    <!-- <el-card v-if="account" class="info-card mb-6">
      <template #header>
        <div class="card-header">
          <h2 class="card-title">基本信息</h2>
        </div>
      </template>
      <div class="info-grid">
        <div class="info-item">
          <h3 class="info-label">沙箱名称</h3>
          <p class="info-value">{{ sandboxName }}</p>
        </div>
        <div class="info-item">
          <h3 class="info-label">创建人</h3>
          <p class="info-value">{{ account.creator }}</p>
        </div>
        <div class="info-item">
          <h3 class="info-label">当前状态</h3>
          <div class="status-wrapper">
            <el-tag
              :type="getStatusType(account.status)"
              effect="light"
              class="status-tag"
            >
              {{ account.status }}
            </el-tag>
          </div>
        </div>
        <div class="info-item">
          <h3 class="info-label">内存占用</h3>
          <div class="memory-usage">
            <span class="usage-text">{{ account.memoryUsage }}%</span>
            <el-progress 
              :percentage="account.memoryUsage"
              :status="getMemoryStatus(account.memoryUsage)"
              :stroke-width="8"
              class="usage-progress"
            />
          </div>
        </div>
      </div>
    </el-card> -->

    <!-- 连接信息 -->
    <el-card  class="connection-card">
      <template #header>
        <div class="card-header">
          <h2 class="card-title">连接信息</h2>
        </div>
      </template>
      <el-table 
        :data="account" 
        style="width: 100%"
        class="connection-table"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: '600'
        }"
        v-loading='dataLoading'
      >
        <!-- <el-table-column prop="sandboxUserId" label="序号" width="80" align="center" /> -->
        <el-table-column label="序号">
          <template #default="{ $index }">{{ $index + 1 }}</template>
        </el-table-column>
        <el-table-column prop="website" label="地址" min-width="180" />
        <el-table-column prop="userName" label="账号" min-width="150" />
        <el-table-column label="密码" min-width="150">
           <template #default="{ row }">
                        <span class="password-mask">{{ row.showPassword ? row.password : '********' }}</span>
                        <el-button link type="primary" @click="showPassword(row)">
                            {{ row.showPassword ? '隐藏' : '查看' }}
                        </el-button>
                    </template>
        </el-table-column>
        
		<el-table-column label="数据库地址" align="center">
		  <template #default="{ row }">
		      {{ row.dbUrl }}
		  </template>
		</el-table-column>
		<el-table-column label="数据库端口" align="center">
		  <template #default="{ row }">
		      {{ row.dbPort }}
		  </template>
		</el-table-column>
		<el-table-column label="数据库用户名" align="center">
		  <template #default="{ row }">
		      {{ row.dbName }}
		  </template>
		</el-table-column>
		<el-table-column label="数据库密码" align="center" min-width="150">
		   <template #default="{ row }">
		            <span class="password-mask">{{ row.showPassword2 ? row.dbPassword : '********' }}</span>
		            <el-button link type="primary" @click="showPassword2(row)">
		                   {{ row.showPassword2 ? '隐藏' : '查看' }}
		            </el-button>
		    </template>
		</el-table-column>
		<el-table-column label="当前状态"  align="center">
		  <template #default="{ row }">
		    <el-tag
		      :type="getStatusType(row.statusStr)"
		      effect="light"
		      class="status-tag"
		    >
		      {{ row.statusStr }}
		    </el-tag>
		  </template>
		</el-table-column>
        <!-- <el-table-column label="操作" width="80" fixed="right" >
                        <template #default="{ row }">
                            <el-button link type="primary" @click="editAccount(row)" class="edit-button">
        
          编辑
        </el-button>
                        </template>
                    </el-table-column> -->
      </el-table>
         <!-- 分页 -->
                <div class="mt-6 flex justify-end">
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalItems"
                        :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        class="custom-pagination" />
                </div>
    </el-card>

   

    <!-- 编辑账号对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑账号"
      width="500px"
      class="edit-dialog"
      destroy-on-close
    >
      <el-form 
        :model="editedAccount" 
        label-width="80px"
        class="edit-form"
      >
        <el-form-item label="地址" required>
          <el-input v-model="editedAccount.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="账号" required>
          <el-input v-model="editedAccount.username" placeholder="请输入账号" autocomplete="off"/>
        </el-form-item>
        <el-form-item label="密码" required>
          <el-input 
            v-model="editedAccount.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
        <el-form-item label="状态" required>
          <el-select v-model="editedAccount.status" style="width: 100%">
             <el-option v-for="status in statusOptions" :key="status" :label="status" :value="status" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAccount">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
    getSandboxDataList
    // editPassword
} from '@/api/systemManagement/sandboxAccountManagement'
import {
    getSandboxUserBinding,
    editSandboxUserBinding
} from '@/api/systemManagement/sandboxAccount.js'
const router = useRouter();
const route = useRoute();
const dataLoading = ref(false)
// 模拟数据 - 沙箱列表
const sandboxes = ref([
  { id: 1, name: '信用评分沙箱' },
  { id: 2, name: '风险预警沙箱' },
  { id: 3, name: '客户画像沙箱' },
  { id: 4, name: '欺诈检测沙箱' },
  { id: 5, name: '数据清洗沙箱' }
]);

// 模拟数据 - 账号详情
const account = ref([]);

// 计算沙箱名称
const sandboxName = computed(() => {
  const sandbox = sandboxes.value.find(s => s.id === account.value.sandboxId);
  return sandbox ? sandbox.name : '未知沙箱';
});

// 状态选项
const statusOptions = ['可用', '临时禁用', '已禁用'];

// 对话框状态
const showEditDialog = ref(false);
// const showPassword = ref(false);

// 编辑表单
const editedAccount = reactive({
  address: '',
  username: '',
  password: '',
  status: '',
  id: '',
  sandboxUserId: ''
});

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '可用':
      return 'success';
    case '临时禁用':
      return 'warning';
    case '禁用':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取内存状态
const getMemoryStatus = (usage) => {
  if (usage > 80) return 'exception';
  if (usage > 50) return 'warning';
  return 'success';
};

// 切换密码显示
const showPassword = (row) => {
  row.showPassword = !row.showPassword;
};
const showPassword2= (row) => {
  row.showPassword2 = !row.showPassword2;
};
// 编辑账号
const editAccount = (row) => {
    getSandboxDataList({
        dictType: 'sandbox_user_status'
     }).then(res => {
        if(res.code == 200){
            statusOptions.value = res.data
            console.log(res, 'sandbox_user_status')
        }
    })
    editedAccount.address = row.website
    editedAccount.username = row.userName
    editedAccount.password = row.password
    editedAccount.status = row.statusStr 
    editedAccount.id = row.id
    editedAccount.sandboxUserId = row.sandboxUserId
    showEditDialog.value = true;
};

// 保存账号
const saveAccount = () => {
  // 简单验证
  


 let params = {
    id: editedAccount.id,
    sandboxUserId: editedAccount.sandboxUserId,
    website: editedAccount.address,
    userName:  editedAccount.username,
    password: editedAccount.password,
    // 0-可用，1-临时禁用，2禁用
    status: editedAccount.status == '可用' ? 0 : (editedAccount.status == '临时禁用' ? 1 : 2)
 }
 console.log('params: ', params)
  editSandboxUserBinding(params).then(res => {
    if(res.code == 200){
       editedAccount.address = undefined
      editedAccount.username = undefined
      editedAccount.password = undefined
      editedAccount.status = undefined
      editedAccount.sandboxUserId = undefined
      editedAccount.id = undefined
      showEditDialog.value = false;
      ElMessage.success('账号信息已更新');
      fetchAccountData()
    }
  })

  
};

// 获取账号数据
const fetchAccountData = async () => {
  console.log('获取当前用户的沙箱账号数据...');
   dataLoading.value = true;
    try {
        let params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }
        
        console.log('获取用户账号数据...', params);
        getSandboxUserBinding(params).then(res => {
            if(res.code == 200){
                account.value = res.rows
                totalItems.value = res.total
                dataLoading.value = false;
            }
            console.log(res)
        })
        // 在实际应用中应调用API
        // userRecords.value = await api.getUsers();
    } finally {
        
    }
  
};

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

const paginatedUsers = ref([])

const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchAccountData()
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchAccountData()
};

onMounted(() => {
  fetchAccountData();
});
</script>

<style scoped>
.custom-pagination {
    --el-pagination-button-bg-color: #fff;
    --el-pagination-hover-color: #409eff;
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
}
.sandbox-account-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-card, .connection-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 20px;
  /* border-bottom: 1px solid #e5e7eb; */
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  padding: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.info-value {
  font-size: 16px;
  color: #1f2937;
  margin: 0;
}

.status-wrapper {
  display: flex;
  align-items: center;
}

.status-tag {
  font-weight: 500;
}

.memory-usage {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usage-text {
  font-size: 14px;
  color: #1f2937;
  min-width: 45px;
}

.usage-progress {
  flex: 1;
}

.connection-table {
  margin: 0;
}

.password-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-text {
  font-family: monospace;
  color: #1f2937;
}

.password-mask {
  font-family: monospace;
  color: #6b7280;
}

.password-toggle {
  padding: 0;
  font-size: 13px;
}

.edit-dialog :deep(.el-dialog__body) {
  padding: 24px 32px;
}

.edit-form {
  max-width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}
</style> 