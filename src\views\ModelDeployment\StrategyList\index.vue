<template>
  <div class="strategy-list-container">
    <div class="page-header">
      <h1 class="page-title">策略列表</h1>
      <el-button type="primary" class="create-btn" @click="openNewStrategyDialog">
        <el-icon class="mr-2"><Plus /></el-icon>
        新建策略
      </el-button>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card" shadow="hover">
      <el-form :model="filters" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="策略ID">
              <el-input v-model="filters.id" placeholder="请输入策略ID" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="策略名称">
              <el-input v-model="filters.name" placeholder="请输入策略名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属业务">
              <el-select v-model="filters.businessArea" placeholder="请选择" class="w-full" clearable>
                <el-option label="全部" value="" />
                <el-option v-for="area in businessAreaOptions" :key="area" :label="area" :value="area" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="策略状态">
              <el-select v-model="filters.status" placeholder="请选择" class="w-full" clearable>
                <el-option label="全部" value="" />
                <el-option v-for="status in statusOptions" :key="status" :label="status" :value="status" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="filter-actions">
          <el-button @click="resetFilters" plain>重置</el-button>
          <el-button type="primary" @click="applyFilters">查询</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="paginatedStrategies" 
        style="width: 100%" 
        v-loading="loading"
        border
        stripe
        highlight-current-row
        class="strategy-table"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="id" label="策略ID" min-width="120" />
        <el-table-column prop="name" label="策略名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="businessArea" label="所属业务" min-width="120" />
        <el-table-column prop="version" label="版本" width="100" align="center" />
        <el-table-column prop="creationTime" label="创建时间" min-width="160" />
        <el-table-column prop="updateTime" label="更新时间" min-width="160" />
        <el-table-column prop="creator" label="创建人" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="light" class="status-tag">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button link type="primary" @click="viewStrategy(row)">查看</el-button>
              <el-button link type="primary" @click="editStrategy(row)">编辑</el-button>
              <el-button 
                v-if="row.status === '草稿' || row.status === '测试中'"
                link 
                type="success" 
                @click="publishStrategy(row.id)">
                发布
              </el-button>
              <el-button 
                v-if="row.status === '已发布'"
                link 
                type="warning" 
                @click="deactivateStrategy(row.id)">
                停用
              </el-button>
              <el-button 
                v-if="row.status === '已停用'"
                link 
                type="success" 
                @click="activateStrategy(row.id)">
                启用
              </el-button>
              <el-button 
                v-if="row.status === '草稿' || row.status === '已停用'"
                link 
                type="danger" 
                @click="deleteStrategy(row.id)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 模拟数据
const allStrategies = ref([
  { id: 'STRAT001', name: '贷前准入策略V1.2', businessArea: '贷前审批', version: '1.2', creationTime: '2024-01-10 09:30:00', updateTime: '2024-02-15 14:00:00', creator: '张三', status: '已发布', description: '针对个人消费贷的初步准入规则集合。' },
  { id: 'STRAT002', name: '贷中风险监控策略A', businessArea: '贷中监控', version: '1.0', creationTime: '2024-01-20 11:00:00', updateTime: '2024-01-22 16:30:00', creator: '李四', status: '测试中', description: '监控存量客户的风险异动。' },
  { id: 'STRAT003', name: '反欺诈基础规则', businessArea: '反欺诈', version: '0.9', creationTime: '2024-02-01 15:00:00', updateTime: '2024-02-05 10:00:00', creator: '王五', status: '草稿', description: '识别常见的欺诈模式。' },
  { id: 'STRAT004', name: '早期催收策略', businessArea: '贷后催收', version: '1.1', creationTime: '2023-12-15 10:00:00', updateTime: '2024-01-05 17:00:00', creator: '赵六', status: '已停用', description: '针对M1逾期客户的催收策略。' },
  { id: 'STRAT005', name: '贷前准入策略V1.3-候选', businessArea: '贷前审批', version: '1.3', creationTime: '2024-03-01 09:00:00', updateTime: '2024-03-05 14:30:00', creator: '张三', status: '草稿', description: 'V1.2的升级版，增加新数据源。' },
  { id: 'STRAT006', name: '贷中大额交易监控', businessArea: '贷中监控', version: '1.1', creationTime: '2024-03-10 10:00:00', updateTime: '2024-03-12 11:00:00', creator: '李四', status: '已发布', description: '监控客户的大额交易行为。' },
  { id: 'STRAT007', name: '信用卡申请反欺诈', businessArea: '反欺诈', version: '2.0', creationTime: '2024-03-15 14:00:00', updateTime: '2024-03-20 09:00:00', creator: '王五', status: '测试中', description: '针对信用卡申请场景的反欺诈模型和规则。' },
  { id: 'STRAT008', name: '小额信贷催收策略', businessArea: '贷后催收', version: '1.0', creationTime: '2024-02-10 16:00:00', updateTime: '2024-02-12 10:30:00', creator: '赵六', status: '已发布', description: '针对小额信贷产品的M1-M2阶段催收。' },
])

const loading = ref(false)
const filters = reactive({
  id: '',
  name: '',
  businessArea: '',
  status: ''
})

const businessAreaOptions = ['贷前审批', '贷中监控', '贷后催收', '反欺诈', '营销']
const statusOptions = ['草稿', '测试中', '已发布', '已停用']

const applyFilters = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  filters.id = ''
  filters.name = ''
  filters.businessArea = ''
  filters.status = ''
  currentPage.value = 1
}

const filteredStrategies = computed(() => {
  return allStrategies.value.filter(strategy => {
    const idMatch = filters.id ? strategy.id.toLowerCase().includes(filters.id.toLowerCase()) : true
    const nameMatch = filters.name ? strategy.name.toLowerCase().includes(filters.name.toLowerCase()) : true
    const businessAreaMatch = filters.businessArea ? strategy.businessArea === filters.businessArea : true
    const statusMatch = filters.status ? strategy.status === filters.status : true
    return idMatch && nameMatch && businessAreaMatch && statusMatch
  }).sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime))
})

// 分页
const currentPage = ref(1)
const pageSize = ref(5)
const totalItems = computed(() => filteredStrategies.value.length)

const paginatedStrategies = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredStrategies.value.slice(start, end)
})

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '草稿': 'info',
    '测试中': 'warning',
    '已发布': 'success',
    '已停用': 'danger'
  }
  return statusMap[status] || 'info'
}

// 操作方法
const openNewStrategyDialog = () => {
  ElMessage.info('打开新建策略对话框/页面（功能待实现）')
}

const viewStrategy = (strategy) => {
  ElMessage.info(`查看策略详情: ${strategy.name} (ID: ${strategy.id})`)
}

const editStrategy = (strategy) => {
  ElMessage.info(`编辑策略: ${strategy.name} (ID: ${strategy.id})`)
}

const publishStrategy = (strategyId) => {
  ElMessageBox.confirm(
    `确定要发布策略 "${strategyId}" 吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const strategy = allStrategies.value.find(s => s.id === strategyId)
    if (strategy) {
      strategy.status = '已发布'
      strategy.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      ElMessage.success('发布成功')
    }
  }).catch(() => {})
}

const deactivateStrategy = (strategyId) => {
  ElMessageBox.confirm(
    `确定要停用策略 "${strategyId}" 吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const strategy = allStrategies.value.find(s => s.id === strategyId)
    if (strategy) {
      strategy.status = '已停用'
      strategy.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      ElMessage.success('停用成功')
    }
  }).catch(() => {})
}

const activateStrategy = (strategyId) => {
  ElMessageBox.confirm(
    `确定要启用策略 "${strategyId}" 吗? 该策略将变为草稿状态。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const strategy = allStrategies.value.find(s => s.id === strategyId)
    if (strategy) {
      strategy.status = '草稿'
      strategy.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      ElMessage.success('启用成功')
    }
  }).catch(() => {})
}

const deleteStrategy = (strategyId) => {
  ElMessageBox.confirm(
    `确定要删除策略 "${strategyId}" 吗? 此操作不可恢复。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    allStrategies.value = allStrategies.value.filter(s => s.id !== strategyId)
    ElMessage.success('删除成功')
    if (paginatedStrategies.value.length === 0 && currentPage.value > 1) {
      currentPage.value--
    }
  }).catch(() => {})
}

// 模拟API获取数据
const fetchStrategies = async () => {
  loading.value = true
  try {
    console.log('获取策略列表数据...')
    // 实际中这里会是API调用
    // allStrategies.value = await api.getStrategies()
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchStrategies()
})
</script>

<style scoped>
.strategy-list-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.create-btn {
  padding: 12px 24px;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.filter-form {
  padding: 8px 0;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.table-card {
  border-radius: 8px;
}

.strategy-table {
  margin: 16px 0;
}

.strategy-table :deep(th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #1f2937;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 16px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style> 