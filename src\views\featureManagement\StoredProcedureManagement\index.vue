<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { procedureApi } from '../utils/api'

const router = useRouter()

// 加载状态
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  name: '',
  tables: '',
  description: '',
  version: '',
  maintainAccount: ''
})

// 存储过程列表
const procedureList = ref([
  {
    id: 1,
    name: '数据清洗存储过程',
    tables: ['raw_data', 'clean_data'],
    description: '清洗原始数据，处理缺失值和异常值',
    version: '1.0.3',
    updateDate: '2024-05-15',
    maintainAccount: 'admin'
  },
  {
    id: 2,
    name: '特征提取存储过程',
    tables: ['clean_data', 'feature_data'],
    description: '从清洗后的数据中提取特征',
    version: '1.1.0',
    updateDate: '2024-05-16',
    maintainAccount: 'admin'
  },
  {
    id: 3,
    name: '企业信息聚合',
    tables: ['enterprise_base', 'enterprise_shareholder', 'tax_info'],
    description: '聚合企业各维度信息',
    version: '1.0.5',
    updateDate: '2024-05-12',
    maintainAccount: 'user1'
  },
  {
    id: 4,
    name: '风险评分计算',
    tables: ['feature_data', 'risk_score'],
    description: '计算企业风险评分',
    version: '2.0.1',
    updateDate: '2024-05-18',
    maintainAccount: 'user2'
  }
])

// 查询存储过程
const handleSearch = async () => {
  loading.value = true
  try {
    const params = { ...filterForm }
    if (params.tables) {
      params.tables = params.tables.split(',').map(item => item.trim())
    }
    
    // 模拟API调用
    setTimeout(() => {
      procedureList.value = procedureList.value.filter(item => {
        const nameMatch = !params.name || item.name.includes(params.name)
        const tablesMatch = !params.tables || params.tables.length === 0 || 
          params.tables.some(table => item.tables.includes(table))
        const descMatch = !params.description || item.description.includes(params.description)
        const versionMatch = !params.version || item.version.includes(params.version)
        const accountMatch = !params.maintainAccount || item.maintainAccount.includes(params.maintainAccount)
        
        return nameMatch && tablesMatch && descMatch && versionMatch && accountMatch
      })
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('筛选存储过程失败:', error)
    loading.value = false
  }
}

// 重置筛选
const resetFilter = () => {
  filterForm.name = ''
  filterForm.tables = ''
  filterForm.description = ''
  filterForm.version = ''
  filterForm.maintainAccount = ''
}

// 查看详情
const viewDetail = (id) => {
  router.push(`/procedure-detail/${id}`)
}

// 新增存储过程
const addProcedure = () => {
  alert('跳转到新增存储过程页面')
  // 实际项目中应该跳转到编辑页面
  // router.push('/procedure-edit/new')
}

// 编辑存储过程
const editProcedure = (id) => {
  alert(`跳转到编辑存储过程页面，ID: ${id}`)
  // 实际项目中应该跳转到编辑页面
  // router.push(`/procedure-edit/${id}`)
}

// 删除存储过程
const deleteProcedure = (id) => {
  if (confirm('确定要删除该存储过程吗？')) {
    procedureList.value = procedureList.value.filter(item => item.id !== id)
  }
}

onMounted(() => {
  // 页面加载时获取存储过程列表
  // fetchProcedures()
})
</script>

<template>
  <div class="procedure-management">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="存储过程名称">
              <el-input v-model="filterForm.name" placeholder="请输入存储过程名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表名称">
              <el-input v-model="filterForm.tables" placeholder="请输入表名称，多个用逗号分隔" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="简介">
              <el-input v-model="filterForm.description" placeholder="请输入简介" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="版本">
              <el-input v-model="filterForm.version" placeholder="请输入版本" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护账号">
              <el-input v-model="filterForm.maintainAccount" placeholder="请输入维护账号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8" class="filter-buttons">
            <el-form-item>
              <el-button @click="resetFilter" plain>重置</el-button>
              <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="success" @click="addProcedure">
        <el-icon><Plus /></el-icon>
        新增存储过程
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="procedureList"
        style="width: 100%"
        border
        stripe
        highlight-current-row
      >
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="name" label="存储过程名称" min-width="180" show-overflow-tooltip />
        <el-table-column label="表名称" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag v-for="table in row.tables" :key="table" class="table-tag">
              {{ table }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="简介" min-width="200" show-overflow-tooltip />
        <el-table-column prop="version" label="版本" width="100" align="center" />
        <el-table-column prop="updateDate" label="更新日期" width="120" align="center" />
        <el-table-column prop="maintainAccount" label="维护账号" width="120" align="center" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button type="primary" link @click="viewDetail(row.id)">查看明细</el-button>
              <el-button type="success" link @click="editProcedure(row.id)">编辑</el-button>
              <el-button type="danger" link @click="deleteProcedure(row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.procedure-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 40px);

  .filter-card {
    margin-bottom: 20px;
    
    .filter-form {
      padding: 20px 0 0;
    }

    .filter-buttons {
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
    }
  }

  .action-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .table-card {
    .table-tag {
      margin-right: 4px;
      margin-bottom: 4px;
    }

    .operation-buttons {
      display: flex;
      justify-content: center;
      gap: 8px;
    }
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  :deep(.el-table) {
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }
    }
  }
}
</style>