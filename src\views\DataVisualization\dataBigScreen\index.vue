<script setup>
import { ref, onMounted } from 'vue'
import Chart from 'chart.js/auto'
import { ElCard, ElRow, ElCol, ElStatistic } from 'element-plus'

// 数据状态
const stats = ref({
  totalVariables: '1,234',
  activeVariables: '856',
  updateFrequency: '日更',
  dataQuality: '98.5%'
})

// 图表引用
let trendChart = null
let distributionChart = null
let timelineChart = null

onMounted(() => {
  // 初始化图表
  initCharts()
})

// 初始化所有图表
const initCharts = () => {
  // 变量趋势图
  const trendCtx = document.getElementById('trendChart')
  if (trendCtx) {
    trendChart = new Chart(trendCtx.getContext('2d'), {
      type: 'line',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '变量数量',
          data: [800, 900, 1000, 1100, 1200, 1234],
          borderColor: 'rgb(64, 158, 255)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 变量分布图
  const distributionCtx = document.getElementById('distributionChart')
  if (distributionCtx) {
    distributionChart = new Chart(distributionCtx.getContext('2d'), {
      type: 'pie',
      data: {
        labels: ['企业基本信息', '经营状况', '信用记录', '其他'],
        datasets: [{
          data: [450, 320, 270, 194],
          backgroundColor: [
            'rgba(64, 158, 255, 0.7)', 
            'rgba(103, 194, 58, 0.7)', 
            'rgba(230, 162, 60, 0.7)',
            'rgba(144, 147, 153, 0.7)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }

  // 时间线图
  const timelineCtx = document.getElementById('timelineChart')
  if (timelineCtx) {
    timelineChart = new Chart(timelineCtx.getContext('2d'), {
      type: 'bar',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '覆盖率',
          data: [0.85, 0.87, 0.9, 0.92, 0.95, 0.985],
          backgroundColor: 'rgba(103, 194, 58, 0.7)'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1,
            ticks: {
              callback: function(value) {
                return (value * 100) + '%'
              }
            }
          }
        }
      }
    })
  }
}

// 获取统计卡片标题
const getStatTitle = (index) => {
  const titles = ['总变量数', '活跃变量数', '更新频率', '数据质量']
  return titles[index]
}

// 获取统计卡片类型
const getStatType = (index) => {
  const types = ['primary', 'success', 'warning', 'info']
  return types[index]
}
</script>

<template>
  <div class="dashboard-container">
    <!-- 数据概览卡片 -->
    <el-row :gutter="24" class="mb-6">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in Object.entries(stats)" :key="index">
        <el-card shadow="hover" class="stat-card" :class="`stat-card-${index}`">
          <el-statistic :title="getStatTitle(index)" :value="stat[1]">
            <template #value>
              <span :class="`text-${getStatType(index)}`">{{ stat[1] }}</span>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="24" class="mb-6">
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>变量趋势图</span>
            </div>
          </template>
          <div class="chart-container">
            <canvas id="trendChart"></canvas>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>变量分布图</span>
            </div>
          </template>
          <div class="chart-container">
            <canvas id="distributionChart"></canvas>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 时间线图表 -->
    <el-card shadow="hover" class="mb-6 timeline-card">
      <template #header>
        <div class="card-header">
          <span>数据覆盖率时间线</span>
        </div>
      </template>
      <div class="chart-container timeline-chart">
        <canvas id="timelineChart"></canvas>
      </div>
    </el-card>

    <!-- 企业维度数据展示 -->
    <el-card shadow="hover" class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>企业维度数据点标记图</span>
        </div>
      </template>
      <el-table :data="[]" border style="width: 100%" class="data-table">
        <el-table-column label="变量\时间" prop="variable" width="120" />
        <el-table-column label="2023年7月" align="center">
          <template #default>
            <el-tag type="success" effect="plain" size="small" class="data-tag">有数据</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="2023年8月" align="center">
          <template #default>
            <el-tag type="success" effect="plain" size="small" class="data-tag">有数据</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="2023年9月" align="center">
          <template #default>
            <el-tag type="danger" effect="plain" size="small" class="data-tag">无数据</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="2023年10月" align="center">
          <template #default>
            <el-tag type="success" effect="plain" size="small" class="data-tag">有数据</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="2023年11月" align="center">
          <template #default>
            <el-tag type="success" effect="plain" size="small" class="data-tag">有数据</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="2023年12月" align="center">
          <template #default>
            <el-tag type="success" effect="plain" size="small" class="data-tag">有数据</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="mt-4 legend-container">
        <el-space>
          <el-tag type="success" effect="plain" size="small" class="legend-tag">有数据</el-tag>
          <el-tag type="danger" effect="plain" size="small" class="legend-tag">无数据</el-tag>
        </el-space>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.chart-container {
  height: 350px;
  position: relative;
  padding: 16px;
}

.timeline-chart {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-card {
  transition: all 0.3s ease;
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-card {
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.timeline-card {
  transition: all 0.3s ease;
}

.timeline-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.data-table-card {
  transition: all 0.3s ease;
}

.data-table-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.text-primary {
  color: #409EFF;
  font-size: 24px;
  font-weight: 600;
}

.text-success {
  color: #67C23A;
  font-size: 24px;
  font-weight: 600;
}

.text-warning {
  color: #E6A23C;
  font-size: 24px;
  font-weight: 600;
}

.text-info {
  color: #909399;
  font-size: 24px;
  font-weight: 600;
}

.mb-6 {
  margin-bottom: 24px;
}

.data-table {
  margin: 16px 0;
}

.data-table :deep(th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.data-tag {
  padding: 4px 8px;
  border-radius: 4px;
}

.legend-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.legend-tag {
  padding: 4px 12px;
  border-radius: 4px;
}

@media screen and (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .timeline-chart {
    height: 350px;
  }
  
  .text-primary,
  .text-success,
  .text-warning,
  .text-info {
    font-size: 20px;
  }
}
</style> 