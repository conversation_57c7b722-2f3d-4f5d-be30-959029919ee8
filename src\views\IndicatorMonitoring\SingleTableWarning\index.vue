<template>
  <div class="single-table-warning p-6 bg-gray-50 min-h-screen memory-alert">
    <h1 class="text-2xl font-bold text-gray-800 mb-8">单表预警</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-8 shadow-md hover:shadow-lg transition-shadow duration-300">
      <el-form :model="filters" label-width="100px" class="filter-form" style="margin-bottom: 16px;">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="数据库名称">
              <el-select v-model="filters.dbName" placeholder="请选择数据库" clearable class="w-full" style="width: 100%;">
                <el-option v-for="db in dbOptions" :key="db" :label="db" :value="db" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表名">
              <el-input v-model="filters.tableName" placeholder="请输入表名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险类型">
              <el-select v-model="filters.riskType" placeholder="请选择风险类型" clearable class="w-full" style="width: 100%;">
                <el-option v-for="type in riskTypeOptions" :key="type" :label="type" :value="type" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险等级">
              <el-select v-model="filters.riskLevel" placeholder="请选择风险等级" clearable class="w-full"
                style="width: 100%;">
                <el-option v-for="level in riskLevelOptions" :key="level" :label="level" :value="level" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预警状态">
              <el-select v-model="filters.alertStatus" placeholder="请选择预警状态" clearable class="w-full"
                style="width: 100%;">
                <el-option v-for="status in alertStatusOptions" :key="status" :label="status" :value="status" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="">
              <el-button @click="resetFilters" class="mr-4">重置</el-button>
              <el-button type="primary" @click="applyFilters">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </el-card>

    <!-- 数据统计卡片 -->
    <el-row :gutter="24" class="mb-8">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card hover:shadow-lg transition-shadow duration-300">
          <template #header>
            <div class="card-header">
              <span class="text-gray-600">总预警数</span>
            </div>
          </template>
          <div class="text-4xl font-bold text-blue-600" style="color: #409EFF; font-size: 24px; font-weight: bold;">{{
            stats.totalAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card hover:shadow-lg transition-shadow duration-300">
          <template #header>
            <div class="card-header">
              <span class="text-gray-600">高风险</span>
            </div>
          </template>
          <div class="text-4xl font-bold text-red-600" style="color: #F56C6C; font-size: 24px; font-weight: bold;">{{
            stats.highRiskAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card hover:shadow-lg transition-shadow duration-300">
          <template #header>
            <div class="card-header">
              <span class="text-gray-600">中风险</span>
            </div>
          </template>
          <div class="text-4xl font-bold text-yellow-600" style="color: #E6A23C; font-size: 24px; font-weight: bold;">{{
            stats.mediumRiskAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6" style="margin: 16px px 0 16px 0;">
        <el-card shadow="hover" class="stat-card hover:shadow-lg transition-shadow duration-300">
          <template #header>
            <div class="card-header">
              <span class="text-gray-600">低风险</span>
            </div>
          </template>
          <div class="text-4xl font-bold text-green-600" style="color: #67C23A; font-size: 24px; font-weight: bold;">{{
            stats.lowRiskAlerts }}</div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 数据表格 -->
    <el-card class="shadow-md hover:shadow-lg transition-shadow duration-300">
      <el-table :data="paginatedAlerts" style="width: 100%" v-loading="loading" border stripe highlight-current-row>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="dbName" label="数据库名称" min-width="120" />
        <el-table-column prop="tableName" label="表名" min-width="120" />
        <el-table-column prop="riskType" label="风险类型" min-width="120" />
        <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRiskLevelType(row.riskLevel)" effect="dark">{{ row.riskLevel }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="alertContent" label="预警内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="firstAlertTime" label="首次预警时间" min-width="160" />
        <el-table-column prop="latestAlertTime" label="最近预警时间" min-width="160" />
        <el-table-column prop="status" label="处理状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="light">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex justify-center space-x-2">
              <el-button type="primary" link @click="viewAlertDetail(row)">查看</el-button>
              <el-button v-if="row.status !== '已处理' && row.status !== '已忽略'" type="success" link
                @click="handleAlert(row.id)">
                处理
              </el-button>
              <el-button v-if="row.status !== '已忽略' && row.status !== '已处理'" type="info" link
                @click="ignoreAlert(row.id)">
                忽略
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-6 flex justify-end" style="display: flex; justify-content: flex-end;">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[8, 16, 24, 32]"
          :total="totalItems" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" background />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 加载状态
const loading = ref(false);

// 模拟数据 - 预警统计
const stats = reactive({
  totalAlerts: 56,
  highRiskAlerts: 12,
  mediumRiskAlerts: 28,
  lowRiskAlerts: 16
});

// 模拟数据 - 预警记录
const alertRecords = ref([
  { id: 1, dbName: 'credit_db', tableName: 'customer_info', riskType: '数据异常', riskLevel: '高', alertContent: '表空间使用率超过95%', firstAlertTime: '2024-05-18 10:30:00', latestAlertTime: '2024-05-20 08:00:00', status: '未处理' },
  { id: 2, dbName: 'credit_db', tableName: 'loan_applications', riskType: '数据缺失', riskLevel: '中', alertContent: '有30%的记录缺少必要字段数据', firstAlertTime: '2024-05-17 14:20:00', latestAlertTime: '2024-05-19 14:00:00', status: '处理中' },
  { id: 3, dbName: 'risk_db', tableName: 'credit_scoring', riskType: '性能问题', riskLevel: '中', alertContent: '查询执行时间超过预期阈值', firstAlertTime: '2024-05-16 09:15:00', latestAlertTime: '2024-05-20 09:15:00', status: '未处理' },
  { id: 4, dbName: 'risk_db', tableName: 'fraud_detection', riskType: '索引异常', riskLevel: '高', alertContent: '主键索引碎片化严重', firstAlertTime: '2024-05-15 16:45:00', latestAlertTime: '2024-05-19 16:45:00', status: '已处理' },
  { id: 5, dbName: 'market_db', tableName: 'customer_segments', riskType: '增长异常', riskLevel: '低', alertContent: '表数据量增长速度异常', firstAlertTime: '2024-05-14 11:30:00', latestAlertTime: '2024-05-18 11:30:00', status: '已忽略' },
  { id: 6, dbName: 'credit_db', tableName: 'payment_history', riskType: '数据异常', riskLevel: '高', alertContent: '连续3天有异常数据插入', firstAlertTime: '2024-05-13 15:20:00', latestAlertTime: '2024-05-18 15:20:00', status: '处理中' },
  { id: 7, dbName: 'market_db', tableName: 'campaign_results', riskType: '权限异常', riskLevel: '中', alertContent: '表权限设置存在安全风险', firstAlertTime: '2024-05-12 14:10:00', latestAlertTime: '2024-05-17 14:10:00', status: '未处理' },
  { id: 8, dbName: 'risk_db', tableName: 'model_features', riskType: '性能问题', riskLevel: '中', alertContent: '表锁定时间过长', firstAlertTime: '2024-05-11 10:25:00', latestAlertTime: '2024-05-16 10:25:00', status: '已处理' },
  { id: 9, dbName: 'credit_db', tableName: 'credit_limits', riskType: '数据缺失', riskLevel: '低', alertContent: '新增数据缺少部分非必要字段', firstAlertTime: '2024-05-10 09:30:00', latestAlertTime: '2024-05-15 09:30:00', status: '已忽略' },
  { id: 10, dbName: 'risk_db', tableName: 'risk_rules', riskType: '备份异常', riskLevel: '高', alertContent: '最近3次表备份失败', firstAlertTime: '2024-05-09 16:40:00', latestAlertTime: '2024-05-14 16:40:00', status: '未处理' },
  { id: 11, dbName: 'market_db', tableName: 'product_offers', riskType: '索引异常', riskLevel: '中', alertContent: '二级索引效率低下', firstAlertTime: '2024-05-08 11:20:00', latestAlertTime: '2024-05-13 11:20:00', status: '处理中' },
  { id: 12, dbName: 'credit_db', tableName: 'transaction_history', riskType: '增长异常', riskLevel: '低', alertContent: '表数据量突增', firstAlertTime: '2024-05-07 09:15:00', latestAlertTime: '2024-05-12 09:15:00', status: '未处理' },
]);

// 筛选条件
const filters = reactive({
  dbName: '',
  tableName: '',
  riskType: '',
  riskLevel: '',
  alertStatus: ''
});

// 选项数据
const dbOptions = ['credit_db', 'risk_db', 'market_db'];
const riskTypeOptions = ['数据异常', '数据缺失', '性能问题', '索引异常', '增长异常', '权限异常', '备份异常'];
const riskLevelOptions = ['高', '中', '低'];
const alertStatusOptions = ['未处理', '处理中', '已处理', '已忽略'];

// 获取风险等级对应的类型
const getRiskLevelType = (level) => {
  const types = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  };
  return types[level] || 'info';
};

// 获取状态对应的类型
const getStatusType = (status) => {
  const types = {
    '未处理': 'danger',
    '处理中': 'warning',
    '已处理': 'success',
    '已忽略': 'info'
  };
  return types[status] || 'info';
};

// 应用筛选
const applyFilters = () => {
  currentPage.value = 1;
};

// 重置筛选
const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  currentPage.value = 1;
};

// 查看预警详情
const viewAlertDetail = (alert) => {
  ElMessageBox.alert(
    `风险等级: ${alert.riskLevel}\n预警内容: ${alert.alertContent}\n首次预警: ${alert.firstAlertTime}\n最近预警: ${alert.latestAlertTime}\n状态: ${alert.status}`,
    `${alert.tableName} - ${alert.riskType}`,
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  );
};

// 处理预警
const handleAlert = (alertId) => {
  ElMessageBox.confirm(
    `确定要处理ID为 "${alertId}" 的预警吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const alert = alertRecords.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = '处理中';
      ElMessage.success('操作成功');
    }
  }).catch(() => { });
};

// 忽略预警
const ignoreAlert = (alertId) => {
  ElMessageBox.confirm(
    `确定要忽略ID为 "${alertId}" 的预警吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const alert = alertRecords.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = '已忽略';
      ElMessage.success('操作成功');
    }
  }).catch(() => { });
};

// 过滤预警记录
const filteredAlerts = computed(() => {
  return alertRecords.value.filter(alert => {
    const dbNameMatch = !filters.dbName || alert.dbName === filters.dbName;
    const tableNameMatch = !filters.tableName || alert.tableName.toLowerCase().includes(filters.tableName.toLowerCase());
    const riskTypeMatch = !filters.riskType || alert.riskType === filters.riskType;
    const riskLevelMatch = !filters.riskLevel || alert.riskLevel === filters.riskLevel;
    const statusMatch = !filters.alertStatus || alert.status === filters.alertStatus;
    return dbNameMatch && tableNameMatch && riskTypeMatch && riskLevelMatch && statusMatch;
  });
});

// 分页
const currentPage = ref(1);
const pageSize = ref(8);
const totalItems = computed(() => filteredAlerts.value.length);

const paginatedAlerts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredAlerts.value.slice(start, end);
});

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 获取预警数据
const fetchAlertData = async () => {
  loading.value = true;
  try {
    console.log('获取单表预警数据...');
    // 在实际应用中应调用API
    // const response = await api.getTableAlerts();
    // alertRecords.value = response.data;
    // stats.totalAlerts = response.stats.totalAlerts;
    // 等等...
  } catch (error) {
    ElMessage.error('获取数据失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchAlertData();
});
</script>

<style scoped>
.single-table-warning {
  background-color: #f5f7fa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card) {
  border-radius: 8px;
  border: none;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa !important;
  font-weight: 600;
}

:deep(.el-pagination) {
  padding: 20px 0;
}

.stat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.stat-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.filter-form {
  padding: 20px 0;
}

.filter-buttons {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-button--primary) {
  padding: 8px 20px;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
}

.memory-alert {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.shadow-md,
.mb-8 {
  margin-bottom: 24px;
}

.text-4xl {
  text-align: left;
  width: 100%;
}
</style>