<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// import { variableApi } from '../utils/api'
import { ElCard, ElButton, ElSkeleton, ElDescriptions, ElDescriptionsItem } from 'element-plus'


const route = useRoute()
const router = useRouter()

// 加载状态
const loading = ref(false)

// 变量详情数据
const variableDetail = ref(null)



// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 页面加载时获取变量详情
onMounted(() => {
  variableDetail.value = JSON.parse(localStorage.getItem('viewDetail')) || {}
  console.log('variableDetail.value', variableDetail.value)
})
</script>

<template>
  <div class="variable-detail">
    <!-- 顶部操作栏 -->
    <div class="header">
      <h2>变量详情</h2>
      <el-button @click="goBack" type="primary" plain>返回</el-button>
    </div>

    <el-skeleton :loading="loading" animated>
      <template #template>
        <el-skeleton-item variant="text" style="width: 100%; height: 200px" />
      </template>

      <template #default>
        <template v-if="variableDetail">
          <el-card class="mb-4">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
              </div>
            </template>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="ID">{{ variableDetail.varId }}</el-descriptions-item>

              <!-- 一级维度、二级维度、三级维度 -->
              <el-descriptions-item label=" 一级维度">{{ variableDetail.firstDimension }}</el-descriptions-item>
              <el-descriptions-item label="二级维度">{{ variableDetail.secondDimension }}</el-descriptions-item>
              <el-descriptions-item label="三级维度">{{ variableDetail.thirdDimension }}</el-descriptions-item>

              <el-descriptions-item label="中文名">{{ variableDetail.chName }}</el-descriptions-item>
              <el-descriptions-item label="英文名">{{ variableDetail.enName }}</el-descriptions-item>
              <el-descriptions-item label="变量类型">{{ variableDetail.varType }}</el-descriptions-item>

              <!-- 回溯状态、最远追溯时间、最近追溯时间、平均覆盖度、调用次数、更新频率、变量密级 -->

              <el-descriptions-item label="回溯状态">{{ variableDetail.backTrackType }}</el-descriptions-item>
              <el-descriptions-item label="最远追溯时间">{{ variableDetail.earliestTime }}</el-descriptions-item>
              <el-descriptions-item label="最近追溯时间">{{ variableDetail.latestTime }}</el-descriptions-item>
              <el-descriptions-item label="平均覆盖度(%)">{{ variableDetail.coverage }}</el-descriptions-item>
              <el-descriptions-item label="调用次数">{{ variableDetail.callCount }}</el-descriptions-item>

              <el-descriptions-item label="更新频率">{{ variableDetail.updateFrequency }}</el-descriptions-item>
              <el-descriptions-item label="变量密级">{{ variableDetail.confidentialityLevel }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="mb-4">
            <template #header>
              <div class="card-header">
                <span>加工逻辑</span>
              </div>
            </template>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="存储过程名称">{{ variableDetail.storedProcedures }}</el-descriptions-item>
              <el-descriptions-item label="表名称">{{ variableDetail.sourceTable }}</el-descriptions-item>
              <el-descriptions-item label="变量格式">{{ variableDetail.varFormat }}</el-descriptions-item>
              <el-descriptions-item label="业务含义">{{ variableDetail.bizMean }}</el-descriptions-item>
            </el-descriptions>
            <div class="mt-4">
              <div class="text-sm font-medium mb-2" style="margin-bottom: 16px">变量加工逻辑</div>
              <el-card class="bg-gray-50">
                <pre class="text-sm">{{ variableDetail.proLogic }}</pre>
              </el-card>
            </div>
          </el-card>

          <el-card class="mb-4">
            <template #header>
              <div class="card-header">
                <span>开发信息</span>
              </div>
            </template>
            <!-- 开发信息：变量加工时间、变量版本、开发状态、当前状况、数据来源、备注 -->

            <el-descriptions :column="3" border>
              <el-descriptions-item label="变量加工时间">{{ variableDetail.proTime }}</el-descriptions-item>
              <el-descriptions-item label="变量版本">{{ variableDetail.version }}</el-descriptions-item>
              <el-descriptions-item label="开发状态">{{ variableDetail.devStatus }}</el-descriptions-item>
              <el-descriptions-item label="当前状态">{{ variableDetail.currentStatus }}</el-descriptions-item>
              <el-descriptions-item label="数据来源">{{ variableDetail.dataSource }}</el-descriptions-item>
            
              <!-- <el-descriptions-item label="备注">{{ variableDetail.remark }}</el-descriptions-item> -->
            </el-descriptions>
              <div class="mt-4">
                <div class="text-sm font-medium mb-2" style="margin-bottom: 16px">备注</div>
                <el-card class="bg-gray-50">
                  <pre class="text-sm">{{ variableDetail.remark }}</pre>
                </el-card>
              </div>
          </el-card>

          <el-card>
            <template #header>
              <div class="card-header">
                <span>账号信息</span>
              </div>
            </template>
            <el-descriptions :column="3" border>
              <!-- 账号信息：逻辑制定账号、变量开发账号、变量维护账号 -->
              <el-descriptions-item label="逻辑制定账号">{{ variableDetail.logicFormulation }}</el-descriptions-item>
              <el-descriptions-item label="变量开发账号">{{ variableDetail.process }}</el-descriptions-item>
              <el-descriptions-item label="变量维护账号">{{ variableDetail.maintainer }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </template>

        <el-empty v-else description="找不到变量信息" />
      </template>
    </el-skeleton>
  </div>
</template>

<style scoped>
.variable-detail {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  font-size: 16px;
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>