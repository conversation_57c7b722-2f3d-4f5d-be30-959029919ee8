import request from '@/utils/request'



// 导入已选变量
export function uploadVarDict(data) {
  return request({
    url: '/biz/ampleMatchResult/uploadVarDict',
    method: 'post',
    data: data
  })
}

// 3.2.2.查询已上传变量
export function viewVarDictPage(data) {
  return request({
    url: '/biz/ampleMatchResult/varDictPage',
    method: 'post',
    data: data
  })
}

// 3.2.3.删除已上传变量
export function removeAmpleMatchResult(data) {
  return request({
    url: '/biz/ampleMatchResult/remove',
    method: 'post',
    data: data
  })
}


// 3.2.4.上传样本
export function uploadAmple(data) {
  return request({
    url: '/biz/ampleMatchResult/uploadAmple',
    method: 'post',
    data: data
  })
}


// /biz/ampleMatchResult/config

export function getconfig(data) {
  return request({
    url: '/biz/ampleMatchResult/config',
    method: 'post',
    data: data
  })
}

//匹配

// /biz/ampleMatchResult/startMatching

export function startMatchingFn(data) {
  return request({
    url: '/biz/ampleMatchResult/startMatching',
    method: 'post',
    data: data
  })
}

//匹配结果
// /biz/ampleMatchResult/page

export function ampleMatchResultFn(data) {
  return request({
    url: '/biz/ampleMatchResult/page',
    method: 'post',
    data: data
  })
}

//下载
export function exportDown(data) {
  return request({
    url: '/biz/ampleMatchResult/downloadTemp',
    method: 'post',
    data: data,
     responseType: 'blob',
  })
}
