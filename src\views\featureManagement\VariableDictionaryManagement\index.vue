<script setup>
import { ref, reactive, onMounted } from 'vue'
// import { variableApi } from '../utils/api'

// 加载状态
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  name: '',
  cnName: '',
  dimension: '',
  tableName: ''
})

// 变量列表
const variableList = ref([
  {
    id: 'VAR001',
    name: 'reg_date',
    cnName: '注册日期',
    dimension: '企业基本信息',
    tableName: 'enterprise_base',
    status: '已上线',
    updateTime: '2024-05-15 10:00:00'
  },
  {
    id: 'VAR002',
    name: 'shareholder',
    cnName: '股东名称',
    dimension: '企业基本信息',
    tableName: 'enterprise_shareholder',
    status: '已上线',
    updateTime: '2024-05-14 16:30:00'
  },
  {
    id: 'VAR003',
    name: 'tax_amount',
    cnName: '纳税金额',
    dimension: '税务信息',
    tableName: 'tax_info',
    status: '已上线',
    updateTime: '2024-05-10 09:20:00'
  },
  {
    id: 'VAR004',
    name: 'invoice_count',
    cnName: '发票数量',
    dimension: '税务信息',
    tableName: 'invoice_info',
    status: '开发中',
    updateTime: '2024-05-18 14:15:00'
  },
  {
    id: 'VAR005',
    name: 'lawsuit_count',
    cnName: '诉讼次数',
    dimension: '司法信息',
    tableName: 'lawsuit_info',
    status: '已上线',
    updateTime: '2024-05-12 11:40:00'
  }
])

// 维度选项
const dimensionOptions = [
  { value: '', label: '全部' },
  { value: '企业基本信息', label: '企业基本信息' },
  { value: '税务信息', label: '税务信息' },
  { value: '司法信息', label: '司法信息' }
]

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '已上线', label: '已上线' },
  { value: '开发中', label: '开发中' },
  { value: '已下线', label: '已下线' }
]

// 编辑模式
const editMode = ref(false)

// 当前编辑的变量
const currentVariable = reactive({
  id: '',
  name: '',
  cnName: '',
  dimension: '',
  tableName: '',
  status: '',
  description: ''
})

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入变量英文名', trigger: 'blur' }],
  cnName: [{ required: true, message: '请输入变量中文名', trigger: 'blur' }],
  dimension: [{ required: true, message: '请选择维度', trigger: 'change' }],
  tableName: [{ required: true, message: '请输入表名', trigger: 'blur' }]
}

const formRef = ref(null)

// 查询变量
const handleSearch = async () => {
  loading.value = true
  try {
    // 模拟API调用
    setTimeout(() => {
      const filtered = [
        {
          id: 'VAR001',
          name: 'reg_date',
          cnName: '注册日期',
          dimension: '企业基本信息',
          tableName: 'enterprise_base',
          status: '已上线',
          updateTime: '2024-05-15 10:00:00'
        },
        {
          id: 'VAR002',
          name: 'shareholder',
          cnName: '股东名称',
          dimension: '企业基本信息',
          tableName: 'enterprise_shareholder',
          status: '已上线',
          updateTime: '2024-05-14 16:30:00'
        }
      ]
      variableList.value = filtered
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('查询变量失败:', error)
    loading.value = false
  }
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.cnName = ''
  filterForm.dimension = ''
  filterForm.tableName = ''
}

// 新增变量
const addVariable = () => {
  editMode.value = true
  Object.keys(currentVariable).forEach(key => {
    currentVariable[key] = ''
  })
  currentVariable.status = '开发中'
}

// 编辑变量
const editVariable = (variable) => {
  editMode.value = true
  Object.keys(currentVariable).forEach(key => {
    if (key in variable) {
      currentVariable[key] = variable[key]
    }
  })
}

// 删除变量
const deleteVariable = (id) => {
  if (confirm('确定要删除该变量吗？')) {
    variableList.value = variableList.value.filter(item => item.id !== id)
  }
}

// 保存变量
const saveVariable = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (currentVariable.id) {
        // 编辑现有变量
        const index = variableList.value.findIndex(item => item.id === currentVariable.id)
        if (index !== -1) {
          variableList.value[index] = { ...currentVariable, updateTime: new Date().toLocaleString() }
        }
      } else {
        // 添加新变量
        const newId = `VAR${String(variableList.value.length + 1).padStart(3, '0')}`
        variableList.value.push({
          ...currentVariable,
          id: newId,
          updateTime: new Date().toLocaleString()
        })
      }
      editMode.value = false
    }
  })
}

// 取消编辑
const cancelEdit = () => {
  editMode.value = false
}

onMounted(() => {
  // 页面加载时可以从API获取数据
})
</script>

<template>
  <div class="variable-dictionary">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline class="filter-form">
        <el-form-item label="变量英文名">
          <el-input v-model="filterForm.name" placeholder="请输入变量英文名" clearable />
        </el-form-item>
        <el-form-item label="变量中文名">
          <el-input v-model="filterForm.cnName" placeholder="请输入变量中文名" clearable />
        </el-form-item>
        <el-form-item label="维度">
          <el-select v-model="filterForm.dimension" placeholder="请选择维度" clearable>
            <el-option
              v-for="option in dimensionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="表名">
          <el-input v-model="filterForm.tableName" placeholder="请输入表名" clearable />
        </el-form-item>
        <el-form-item class="filter-buttons">
          <el-button @click="resetFilter" plain>重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="success" @click="addVariable" class="add-button">
        <el-icon><Plus /></el-icon>新增变量
      </el-button>
    </div>

    <!-- 编辑表单 -->
    <el-dialog
      v-model="editMode"
      :title="currentVariable.id ? '编辑变量' : '新增变量'"
      width="50%"
      destroy-on-close
      class="edit-dialog"
    >
      <el-form
        ref="formRef"
        :model="currentVariable"
        :rules="rules"
        label-width="100px"
        class="edit-form"
      >
        <el-form-item label="变量英文名" prop="name">
          <el-input v-model="currentVariable.name" placeholder="请输入变量英文名" />
        </el-form-item>
        <el-form-item label="变量中文名" prop="cnName">
          <el-input v-model="currentVariable.cnName" placeholder="请输入变量中文名" />
        </el-form-item>
        <el-form-item label="维度" prop="dimension">
          <el-select v-model="currentVariable.dimension" placeholder="请选择维度" class="w-full">
            <el-option
              v-for="option in dimensionOptions.filter(item => item.value)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="表名" prop="tableName">
          <el-input v-model="currentVariable.tableName" placeholder="请输入表名" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="currentVariable.status" placeholder="请选择状态" class="w-full">
            <el-option
              v-for="option in statusOptions.filter(item => item.value)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="变量描述" prop="description">
          <el-input
            v-model="currentVariable.description"
            type="textarea"
            rows="3"
            placeholder="请输入变量描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveVariable">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="variableList"
        style="width: 100%"
        border
        stripe
        highlight-current-row
      >
        <el-table-column prop="id" label="变量ID" width="100" />
        <el-table-column prop="name" label="变量英文名" min-width="120" />
        <el-table-column prop="cnName" label="变量中文名" min-width="120" />
        <el-table-column prop="dimension" label="维度" min-width="120" />
        <el-table-column prop="tableName" label="表名" min-width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === '已上线' ? 'success' : 
                     row.status === '开发中' ? 'warning' : 'danger'"
              effect="light"
              class="status-tag"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="160" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="editVariable(row)">编辑</el-button>
            <el-button type="danger" link @click="deleteVariable(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.variable-dictionary {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 40px);

  .filter-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 10px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;
      }

      .filter-buttons {
        margin-left: auto;
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;

    .add-button {
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .table-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .status-tag {
      min-width: 60px;
      text-align: center;
    }
  }

  .edit-dialog {
    .edit-form {
      padding: 20px;
    }

    .dialog-footer {
      text-align: right;
      padding-top: 20px;
    }
  }

  :deep(.el-card__body) {
    padding: 15px;
  }

  :deep(.el-table) {
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }
    }
  }
}

.w-full {
  width: 100%;
}
</style>