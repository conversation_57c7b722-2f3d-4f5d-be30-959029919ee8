import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout/index";
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";

// 匹配views里面所有的.vue文件
const modules = import.meta.glob("./../../views/**/*.vue");
// console.log("Available modules:", Object.keys(modules));
const usePermissionStore = defineStore("permission", {
  state: () => ({
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  }),
  actions: {
    setRoutes(routes) {
      this.addRoutes = routes;
      this.routes = constantRoutes.concat(routes);
    },
    setDefaultRoutes(routes) {
      this.defaultRoutes = constantRoutes.concat(routes);
    },
    setTopbarRoutes(routes) {
      this.topbarRouters = routes;
    },
    setSidebarRouters(routes) {
      this.sidebarRouters = routes;
    },
    generateRoutes(roles) {
      return new Promise((resolve) => {
        // 向后端请求路由数据
        getRouters().then((res) => {
          console.log(res, 'res--- getRouters')

          const sdata = JSON.parse(JSON.stringify(res.data))
          const rdata = JSON.parse(JSON.stringify(res.data))
          const defaultData = JSON.parse(JSON.stringify(res.data))

          // const sdata = JSON.parse(JSON.stringify(res.data))
          // const rdata = JSON.parse(JSON.stringify(res.data))
          // const defaultData = JSON.parse(JSON.stringify(res.data))


          // console.log(
          //   JSON.parse(JSON.stringify(res.data)),
          //   "JSON.parse(JSON.stringify(res.data))"
          // );

          // const sdata = [
          //     {
          //     name: "dataVerification",
          //     path: "/dataVerification",
          //     redirect: "noRedirect",
          //     hidden: false,
          //     component: "dataVerification/index",
          //     meta: {
          //       icon: "yrz-01",
          //       isLeft: "0",
          //       leftLevel: null,
          //       link: null,
          //       noCache: false,
          //       projectType: null,
          //       title: "人工核对",
          //     },
          //     alwaysShow: false,
          //     component: Layout,
          //   },
          //   //  {
          //   //   name: "DataVisualization",
          //   //   path: "/DataVisualization",
          //   //   redirect: "/DataVisualization/dataBigScreen",
          //   //   hidden: false,
          //   //   meta: {
          //   //     icon: "icon_picture",
          //   //     isLeft: "0",
          //   //     leftLevel: null,
          //   //     link: null,
          //   //     noCache: false,
          //   //     projectType: null,
          //   //     title: "数据可视化",
          //   //   },
          //   //   alwaysShow: true,
          //   //   component: Layout,
          //   //   children: [
          //   //     {
          //   //       name: "dataBigScreen",
          //   //       path: "dataBigScreen",
          //   //       hidden: false,
          //   //       component: "DataVisualization/dataBigScreen/index.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "0",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "数据大屏",
          //   //       },
          //   //     },
          //   //     {
          //   //       name: "variableCoverageStatistics",
          //   //       path: "variableCoverageStatistics",
          //   //       hidden: false,
          //   //       component: "DataVisualization/variableCoverageStatistics/indexindex.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "变量覆盖统计",
          //   //       },
          //   //     },
          //   //   ],
          //   // },


          //   {
          //     name: "featureSelection",
          //     path: "/featureSelection",
          //     redirect: "/featureSelection/variableDictionary",
          //     hidden: false,
          //     meta: {
          //       icon: "icon_picture",
          //       isLeft: "0",
          //       leftLevel: null,
          //       link: null,
          //       noCache: false,
          //       projectType: null,
          //       title: "特征筛选",
          //     },
          //     alwaysShow: true,
          //     component: Layout,
          //     children: [
          //       {
          //         name: "variableDictionary",
          //         path: "variableDictionary",
          //         hidden: false,
          //         component: "featureSelection/variableDictionary/index.vue",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "0",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "变量字典",
          //         },
          //       },
          //       // {
          //       //   name: "variableDerivation",
          //       //   path: "variableDerivation",
          //       //   hidden: false,
          //       //   component: "featureSelection/variableDerivation/indexindex.vue",
          //       //   meta: {
          //       //     icon: "xitongguanli",
          //       //     isLeft: "1",
          //       //     leftLevel: null,
          //       //     link: null,
          //       //     noCache: false,
          //       //     projectType: null,
          //       //     title: "变量衍生",
          //       //   },
          //       // },
          //     {
          //         name: "sampleMatching",
          //         path: "sampleMatching",
          //         hidden: false,
          //         component: "featureSelection/sampleMatching/index",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "1",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "样本匹配",
          //         },
          //       },
          //     ],
          //   },

          //   //   {
          //   //   name: "featureManagement",
          //   //   path: "/featureManagement",
          //   //   redirect: "/featureManagement/VariableDictionaryManagement",
          //   //   hidden: false,
          //   //   meta: {
          //   //     icon: "icon_picture",
          //   //     isLeft: "0",
          //   //     leftLevel: null,
          //   //     link: null,
          //   //     noCache: false,
          //   //     projectType: null,
          //   //     title: "特征管理",
          //   //   },
          //   //   alwaysShow: true,
          //   //   component: Layout,
          //   //   children: [
          //   //     {
          //   //       name: "VariableDictionaryManagement",
          //   //       path: "VariableDictionaryManagement",
          //   //       hidden: false,
          //   //       component: "featureManagement/VariableDictionaryManagement/index.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "0",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "变量字典管理",
          //   //       },
          //   //     },
          //   //     {
          //   //       name: "StoredProcedureManagement",
          //   //       path: "StoredProcedureManagement",
          //   //       hidden: false,
          //   //       component: "featureManagement/StoredProcedureManagement/indexindex.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "存储过程管理",
          //   //       },
          //   //     },
          //   //   {
          //   //       name: "TaskSchedulingManagement",
          //   //       path: "TaskSchedulingManagement",
          //   //       hidden: false,
          //   //       component: "featureManagement/TaskSchedulingManagement/index",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "任务调度管理",
          //   //       },
          //   //     },
          //   //   ],
          //   // },


          //   //   {
          //   //   name: "ModelDeployment",
          //   //   path: "/ModelDeployment",
          //   //   redirect: "/ModelDeployment/ModelingHistory",
          //   //   hidden: false,
          //   //   meta: {
          //   //     icon: "icon_picture",
          //   //     isLeft: "0",
          //   //     leftLevel: null,
          //   //     link: null,
          //   //     noCache: false,
          //   //     projectType: null,
          //   //     title: "模型部署",
          //   //   },
          //   //   alwaysShow: true,
          //   //   component: Layout,
          //   //   children: [
          //   //     {
          //   //       name: "ModelingHistory",
          //   //       path: "ModelingHistory",
          //   //       hidden: false,
          //   //       component: "ModelDeployment/ModelingHistory/index.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "0",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "建模历史",
          //   //       },
          //   //     },
          //   //     {
          //   //       name: "ModelList",
          //   //       path: "ModelList",
          //   //       hidden: false,
          //   //       component: "ModelDeployment/ModelList/indexindex.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "模型列表",
          //   //       },
          //   //     },
          //   //   {
          //   //       name: "StrategyHistory",
          //   //       path: "StrategyHistory",
          //   //       hidden: false,
          //   //       component: "ModelDeployment/StrategyHistory/index",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "策略历史",
          //   //       },
          //   //     },
          //   //        {
          //   //       name: "StrategyList",
          //   //       path: "StrategyList",
          //   //       hidden: false,
          //   //       component: "ModelDeployment/StrategyList/index",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "策略列表",
          //   //       },
          //   //     },
          //   //   ],
          //   // },


          //   //  {
          //   //   name: "IndicatorMonitoring",
          //   //   path: "/IndicatorMonitoring",
          //   //   redirect: "/IndicatorMonitoring/SingleTableWarning",
          //   //   hidden: false,
          //   //   meta: {
          //   //     icon: "icon_picture",
          //   //     isLeft: "0",
          //   //     leftLevel: null,
          //   //     link: null,
          //   //     noCache: false,
          //   //     projectType: null,
          //   //     title: "指标监控",
          //   //   },
          //   //   alwaysShow: true,
          //   //   component: Layout,
          //   //   children: [
          //   //     {
          //   //       name: "SingleTableWarning",
          //   //       path: "SingleTableWarning",
          //   //       hidden: false,
          //   //       component: "IndicatorMonitoring/SingleTableWarning/index.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "0",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "预警推送-单表预警",
          //   //       },
          //   //     },
          //   //     {
          //   //       name: "MemoryAlert",
          //   //       path: "MemoryAlert",
          //   //       hidden: false,
          //   //       component: "IndicatorMonitoring/MemoryAlert/indexindex.vue",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "预警推送-内存预警",
          //   //       },
          //   //     },
          //   //   {
          //   //       name: "BatchProgress",
          //   //       path: "BatchProgress",
          //   //       hidden: false,
          //   //       component: "IndicatorMonitoring/BatchProgress/index",
          //   //       meta: {
          //   //         icon: "xitongguanli",
          //   //         isLeft: "1",
          //   //         leftLevel: null,
          //   //         link: null,
          //   //         noCache: false,
          //   //         projectType: null,
          //   //         title: "任务监控-跑批进度",
          //   //       },
          //   //     },
          //   //   ],
          //   // },

          //   {
          //     name: "systemManagement",
          //     path: "/systemManagement",
          //     redirect: "/systemManagement/accountManagement",
          //     hidden: false,
          //     meta: {
          //       icon: "icon_picture",
          //       isLeft: "0",
          //       leftLevel: null,
          //       link: null,
          //       noCache: false,
          //       projectType: null,
          //       title: "系统管理",
          //     },
          //     alwaysShow: true,
          //     component: Layout,
          //     children: [
          //       {
          //         name: "accountManagement",
          //         path: "accountManagement",
          //         hidden: false,
          //         component: "systemManagement/accountManagement/index.vue",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "0",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "账号管理",
          //         },
          //       },
          //       {
          //         name: "roleManagement",
          //         path: "roleManagement",
          //         hidden: false,
          //         component: "systemManagement/roleManagement/indexindex.vue",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "1",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "角色管理",
          //         },
          //       },
          //     {
          //         name: "sandboxAccountManagement",
          //         path: "sandboxAccountManagement",
          //         hidden: false,
          //         component: "systemManagement/sandboxAccountManagement/index",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "1",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "沙箱账号管理",
          //         },
          //       },
          //           {
          //         name: "sandboxAccount",
          //         path: "sandboxAccount",
          //         hidden: false,
          //         component: "systemManagement/sandboxAccount/index",
          //         meta: {
          //           icon: "xitongguanli",
          //           isLeft: "1",
          //           leftLevel: null,
          //           link: null,
          //           noCache: false,
          //           projectType: null,
          //           title: "沙箱账号",
          //         },
          //       },
          //     ],
          //   },
           
          // ];
          // const rdata = sdata;
          // const defaultData = rdata;
          const sidebarRoutes = filterAsyncRouter(sdata);
          const rewriteRoutes = filterAsyncRouter(rdata, false, true);
          const defaultRoutes = filterAsyncRouter(defaultData);
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
          asyncRoutes.forEach((route) => {
            router.addRoute(route);
          });
          // console.log("Generated Routes:", rewriteRoutes);
          this.setRoutes(rewriteRoutes);
          this.setSidebarRouters(constantRoutes.concat(sidebarRoutes));
          this.setDefaultRoutes(sidebarRoutes);
          this.setTopbarRoutes(defaultRoutes);
          resolve(rewriteRoutes);
        });
      });
    },
  },
});

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else if (route.component === "InnerLink") {
        route.component = InnerLink;
      } else {
        route.component = loadView(route.component);
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = [];
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === "ParentView" && !lastRouter) {
        el.children.forEach((c) => {
          c.path = el.path + "/" + c.path;
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
            return;
          }
          children.push(c);
        });
        return;
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
      if (el.children && el.children.length) {
        children = children.concat(filterChildren(el.children, el));
        return;
      }
    }
    children = children.concat(el);
  });
  return children;
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = [];
  routes.forEach((route) => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route);
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route);
      }
    }
  });
  return res;
}

export const loadView = (view) => {
  for (const path in modules) {
    const normalizedPath = path.replace("../../views/", "").replace(".vue", "");
    if (normalizedPath === view) {
      return () => modules[path]();
    }
  }
  console.warn(`[路由加载失败] 未找到组件路径: ${view}`);
  return null;
};

export default usePermissionStore;
