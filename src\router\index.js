import { createWebHistory, createRouter } from "vue-router";
/* Layout */
import Layout from "@/layout";
console.log('rotue---加载')
/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/largeScreen",
    component: () => import("@/views/largeScreen"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "/index",
    // component: Layout,
    redirect: "/largeScreen",
    hidden: false,
  },
  {
    path: "",
    // component: Layout,
    redirect: "/largeScreen",
    hidden: false,
    // children: [
    //   {
    //     path: "/index",
    //     component: () => import("@/views/index"),
    //     name: "Index",
    //     meta: { title: "欢迎页", icon: "icon_overview", affix: true, hidden: false },
    //   },
    // ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "修改密码", icon: "user" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [

  //   {
  //   path: "",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:user:edit"],
  //   redirect: "/featureSelection",
  //   children: [
  //     {
  //       path: "/DataVisualization/dataBigScreen",
  //       component: () =>
  //         import(
  //           "@/views/DataVisualization/dataBigScreen/index"
  //         ),
  //       name: "数据大屏",
  //       meta: {
  //         title: "数据大屏",
  //         icon: "none",
  //         activeMenu: "/DataVisualization/dataBigScreen",
  //       },
  //     },
     
  //     {
  //       path: "/DataVisualization/variableCoverageStatistics",
  //       component: () =>
  //         import("@/views/DataVisualization/variableCoverageStatistics/index"),
  //       name: "变量覆盖统计",
  //       meta: {
  //         title: "变量覆盖统计",
  //         icon: "none",
  //         activeMenu: "/DataVisualization/variableCoverageStatistics",
  //       },
  //     },
  //   ],
  // },

  {
    path: "/featureSelection",
    component: Layout,
    hidden: true,

    // permissions: ["system:user:edit"],
    redirect: "/featureSelection/variableDictionary",
    children: [
      {
        path: "/featureSelection/variableDictionary",
        component: () =>
          import(
            "@/views/featureSelection/variableDictionary/index"
          ),
        name: "变量字典",
        meta: {
          title: "变量字典",
          icon: "none",
          noCache: false,
          activeMenu: "/featureSelection/variableDictionary",
        },
      },
      {
        path: "/featureSelection/VariableDetail",
        component: () =>
          import(
            "@/views/featureSelection/variableDictionary/VariableDetail"
          ),
        name: "VariableDetail",
        meta: {
          title: "变量字典详情",
          icon: "none",
          noCache: true,
          activeMenu: "/featureSelection/VariableDetail",
        },
      },
      {
        path: "/featureSelection/variableDerivation",
        component: () => import("@/views/featureSelection/variableDerivation/index"),
        name: "变量衍生",
        meta: {
          title: "变量衍生",
          icon: "none",
          activeMenu: "/featureSelection/variableDerivation",
        },
      },
       {
        path: "/featureSelection/variableDerivationDetail",
        component: () =>
          import("@/views/featureSelection/variableDerivation/variableDerivationDetail"),
        name: "变量衍生明细",
        meta: {
          title: "变量衍生明细",
          icon: "none",
          activeMenu: "/featureSelection/variableDerivationDetail",
        },
      },
       {
        path: "/featureSelection/sampleMatching",
        component: () =>
          import("@/views/featureSelection/sampleMatching/index"),
        name: "样本匹配",
        meta: {
          title: "样本匹配",
          icon: "none",
          activeMenu: "/featureSelection/sampleMatching",
        },
      },


    ],
  },


  // {
  //   path: "/featureManagement",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:user:edit"],
  //   redirect: "/featureManagement/VariableDictionaryManagement",
  //   children: [
  //     {
  //       path: "/featureManagement/VariableDictionaryManagement",
  //       component: () =>
  //         import(
  //           "@/views/featureManagement/VariableDictionaryManagement/index"
  //         ),
  //       name: "变量字典管理",
  //       meta: {
  //         title: "变量字典管理",
  //         icon: "none",
  //         activeMenu: "/featureManagement/VariableDictionaryManagement",
  //       },
  //     },
  //     {
  //       path: "/featureManagement/StoredProcedureManagement",
  //       component: () =>
  //         import("@/views/featureManagement/StoredProcedureManagement/index"),
  //       name: "存储过程管理",
  //       meta: {
  //         title: "存储过程管理",
  //         icon: "none",
  //         activeMenu: "/featureManagement/StoredProcedureManagement",
  //       },
  //     },
  //     {
  //       path: "/featureManagement/TaskSchedulingManagement",
  //       component: () =>
  //         import("@/views/featureManagement/TaskSchedulingManagement/index"),
  //       name: "任务调度管理",
  //       meta: {
  //         title: "任务调度管理",
  //         icon: "none",
  //         activeMenu: "/featureManagement/TaskSchedulingManagement",
  //       },
  //     },
       


  //   ],
  // },

  // {
  //   path: "/ModelDeployment",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:user:edit"],
  //   redirect: "/ModelDeployment/ModelingHistory",
  //   children: [
  //     {
  //       path: "/ModelDeployment/ModelingHistory",
  //       component: () =>
  //         import(
  //           "@/views/ModelDeployment/ModelingHistory/index"
  //         ),
  //       name: "建模历史",
  //       meta: {
  //         title: "建模历史",
  //         icon: "none",
  //         activeMenu: "/ModelDeployment/ModelingHistory",
  //       },
  //     },
  //     {
  //       path: "/ModelDeployment/ModelList",
  //       component: () =>
  //         import("@/views/ModelDeployment/ModelList/index"),
  //       name: "模型列表",
  //       meta: {
  //         title: "模型列表",
  //         icon: "none",
  //         activeMenu: "/ModelDeployment/ModelList",
  //       },
  //     },
  //     {
  //       path: "/ModelDeployment/StrategyHistory",
  //       component: () =>
  //         import("@/views/ModelDeployment/StrategyHistory/index"),
  //       name: "策略历史",
  //       meta: {
  //         title: "策略历史",
  //         icon: "none",
  //         activeMenu: "/ModelDeployment/StrategyHistory",
  //       },
  //     },

  //     {
  //       path: "/ModelDeployment/StrategyList",
  //       component: () =>
  //         import("@/views/ModelDeployment/StrategyList/index"),
  //       name: "策略列表",
  //       meta: {
  //         title: "策略列表",
  //         icon: "none",
  //         activeMenu: "/ModelDeployment/StrategyList",
  //       },
  //     },
       


  //   ],
  // },

  //  {
  //   path: "/IndicatorMonitoring",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:user:edit"],
  //   redirect: "/IndicatorMonitoring/SingleTableWarning",
  //   children: [
  //     {
  //       path: "/IndicatorMonitoring/SingleTableWarning",
  //       component: () =>
  //         import(
  //           "@/views/IndicatorMonitoring/SingleTableWarning/index"
  //         ),
  //       name: "预警推送-单表预警",
  //       meta: {
  //         title: "预警推送-单表预警",
  //         icon: "none",
  //         activeMenu: "/IndicatorMonitoring/SingleTableWarning",
  //       },
  //     },
     
  //     {
  //       path: "/IndicatorMonitoring/MemoryAlert",
  //       component: () =>
  //         import("@/views/IndicatorMonitoring/MemoryAlert/index"),
  //       name: "预警推送-内存预警",
  //       meta: {
  //         title: "预警推送-内存预警",
  //         icon: "none",
  //         activeMenu: "/IndicatorMonitoring/MemoryAlert",
  //       },
  //     },

  //     {
  //       path: "/IndicatorMonitoring/BatchProgress",
  //       component: () =>
  //         import("@/views/IndicatorMonitoring/BatchProgress/index"),
  //       name: "任务监控-跑批进度",
  //       meta: {
  //         title: "任务监控-跑批进度",
  //         icon: "none",
  //         activeMenu: "/IndicatorMonitoring/BatchProgress",
  //       },
  //     },
       


  //   ],
  // },
 
 // 沙箱建模
 {
  path: "/sandboxModel",
  component: Layout,
  hidden: true,
  permissions: ["system:user:edit"],
  redirect: "/sandboxModel/sandboxAccount",
  children: [
    {
      path: "/sandboxModel/sandboxAccount",
      component: () =>
        import(
          "@/views/sandboxModel/sandboxAccount/index"
        ),
      name: "沙箱账号",
      meta: {
        title: "沙箱账号",
        icon: "none",
        activeMenu: "/sandboxModel/sandboxAccount",
      },
    },
 ]
 },
 
 

   {
    path: "/systemManagement",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    redirect: "/systemManagement/accountManagement",
    children: [
      {
        path: "/systemManagement/accountManagement",
        component: () =>
          import(
            "@/views/systemManagement/accountManagement/index"
          ),
        name: "账号管理",
        meta: {
          title: "账号管理",
          icon: "none",
          activeMenu: "/systemManagement/accountManagement",
        },
      },
      {
        path: "/systemManagement/roleManagement",
        component: () => import("@/views/systemManagement/roleManagement/index"),
        name: "角色管理",
        meta: {
          title: "角色管理",
          icon: "none",
          activeMenu: "/systemManagement/roleManagement",
        },
      },

       {
        path: "/roleManagement/dataDetail",
        component: () =>
          import("@/views/systemManagement/roleManagement/dataDetail"),
        name: "数据权限配置",
        meta: {
          title: "数据权限配置",
          icon: "none",
          activeMenu: "/systemManagement/roleManagement",
        },
      },


      {
        path: "/systemManagement/sandboxAccountManagement",
        component: () =>
          import("@/views/systemManagement/sandboxAccountManagement/index"),
        name: "沙箱账号管理",
        meta: {
          title: "沙箱账号管理",
          icon: "none",
          activeMenu: "/systemManagement/sandboxAccountManagement",
        },
      },

      // {
      //   path: "/systemManagement/sandboxAccount",
      //   component: () =>
      //     import("@/views/systemManagement/sandboxAccount/index"),
      //   name: "沙箱账号",
      //   meta: {
      //     title: "沙箱账号",
      //     icon: "none",
      //     activeMenu: "/systemManagement/sandboxAccount",
      //   },
      // },
       


    ],
  },

];

const router = createRouter({
  history: createWebHistory(),
  routes: [...constantRoutes, ...dynamicRoutes],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
