<template>
  <div class="app-container">
    <!-- 头部区域 -->
    <div class="main-header">
      <div class="header-content">
        <div class="left-section">
          <h2 class="title">人工核对</h2>
        </div>
        <el-button type="primary" :icon="Refresh" @click="refreshList"
          >刷新列表</el-button
        >
      </div>
    </div>

    <div class="main-content">
      <div class="content-wrapper">
        <!-- 左侧文件列表 -->
        <div class="file-panel">
          <el-card class="search-filter">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名"
              :prefix-icon="Search"
              clearable
            />
            <div class="filter-tabs">
              <el-radio-group v-model="filterStatus" size="small">
                <el-radio-button label="全部" />
                <el-radio-button label="待核对" />
                <el-radio-button label="已完成" />
              </el-radio-group>
            </div>
          </el-card>
          <el-card style="height: 540px;overflow-y: scroll;">
            <template #header>
              <div class="card-header">待核对文件 ({{ filteredFiles.length }})</div>
            </template>
            <el-scrollbar class="file-list">
              <div
                v-for="(file, index) in paginatedFiles"
                :key="file.id"
                class="file-item"
                :class="{ selected: selectedFile === index + (currentPage - 1) * pageSize }"
                @click="selectFile(index + (currentPage - 1) * pageSize)"
              >
                <div class="file-icon">
				   <img 
				                      class="file-thumbnail" 
				                      :src="file.thumbnail" 
				                      alt="" 
				                      @click.stop="showImagePreview(file.thumbnail)"
				                      style="cursor: pointer;"
				                    />
                </div>
                <div class="file-info">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-folder">
                    {{ file.folder }}
                  </div>
                  <el-tag :type="statusType(file.status)" size="small">
                    {{ file.status }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
			<div class="pagination-wrapper">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-size="pageSize"
              layout="total, prev, pager, next, jumper"
              :total="filteredFiles.length"
            />
			</div>
          </el-card>
        </div>
		 <!-- 图片预览对话框 -->
		        <el-dialog
		          v-model="imagePreviewVisible"
		          :title="previewImageTitle"
		          width="80%"
		          :before-close="handleImagePreviewClose"
		        >
		          <img :src="previewImageUrl" style="width: 100%;" />
		        </el-dialog>

        <!-- 右侧工作区 -->
        <div class="workspace">
          <!-- 操作工具栏 -->
          <el-card class="toolbar">
            <div class="current-file">
              当前核对：<span class="highlight blue-text">{{ currentFile.name }}</span>
            </div>
            <div class="actions">
              <el-button type="success" :icon="Check" @click="confirmSave"
                >确认并入库</el-button
              >
              <el-button
                type="danger"
                :icon="Warning"
                @click="showProblemPanel = !showProblemPanel"
              >
                标记问题
              </el-button>
            </div>
          </el-card>

          <!-- 图片和表格区域 -->
          <div class="grid-container">
            <!-- 图片预览 -->
            <el-card class="image-preview">
              <template #header>
                <div class="card-header">原图</div>
              </template>
              <el-scrollbar>
                <img 
                  :src="currentFile.thumbnail" 
                  class="preview-image" 
                  @click="showImagePreview(currentFile.full)"
                  style="cursor: pointer;"
                />
              </el-scrollbar>
            </el-card>

            <!-- 表格编辑 -->
            <el-card class="data-editor">
              <template #header>
                <div class="card-header">
                  <span>数据核对</span>
                  <div class="header-actions">
                    <el-button size="small" :icon="Plus" @click="addRow"
                      >添加行</el-button
                    >
                    <el-button size="small" :icon="Document" plain
                      >导出</el-button
                    >
                  </div>
                </div>
              </template>

              <div class="editor-content">
			
               <el-input
                  v-model="tableData[0].title"
                  placeholder="表格标题"
                  class="title-input"
                />
				
                 <el-table :data="tableData" border class="data-table">
                  <el-table-column prop="subject" label="科目">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.subject"
                        :class="{ 'highlight-row': row.highlight }"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="current" label="期末余额">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.current"
                        :class="{ 'highlight-row': row.highlight }"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="initial" label="年初余额">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.initial"
                        :class="{ 'highlight-row': row.highlight }"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="60" fixed="right">
                    <template #default="{ $index }">
                      <el-button
                        type="danger"
                        :icon="Delete"
                        size="small"
                        @click="deleteRow($index)"
                      />
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 问题标记面板 -->
                <el-card v-show="showProblemPanel" class="problem-panel">
                  <el-form label-width="80px">
                    <el-form-item label="问题类型">
                      <el-radio-group v-model="problem.type">
                        <el-radio label="数据与图片不符" />
                        <el-radio label="识别准确度低" />
                        <el-radio label="格式不支持" />
                        <el-radio label="其他问题" />
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注说明">
                      <el-input
                        v-model="problem.remark"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入问题描述..."
                      />
                    </el-form-item>
                    <div class="form-actions">
                      <el-button type="primary" @click="submitProblem"
                        >确认标记</el-button
                      >
                      <el-button @click="showProblemPanel = false"
                        >取消</el-button
                      >
                    </div>
                  </el-form>
                </el-card>
              </div>
            </el-card>
          </div>

          <!-- 底部操作 -->
          <div class="footer-actions">
            <el-alert type="warning" show-icon :closable="false">
              <template #default>
                <span class="tip-text">
                  标黄行表示可能存在问题的数据，请仔细核对。可直接编辑修改表格中的内容，完成后点击"确认并入库"
                </span>
              </template>
            </el-alert>
            <div class="navigation">
              <el-button :icon="ArrowLeft" @click="prevFile">上一个</el-button>
              <el-button  @click="nextFile">下一个 <el-icon class="el-icon--right"><ArrowRight /></el-icon></el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  Refresh,
  Check,
  Warning,
  Plus,
  Document,
  Delete,
  ArrowLeft,
  ArrowRight,
  Search,
} from "@element-plus/icons-vue";
// 图片预览相关
// 此处代码无需修改，复用之前实现的图片预览逻辑
const imagePreviewVisible = ref(false);
const previewImageUrl = ref("");
const previewImageTitle = ref("图片预览");

const showImagePreview = (url) => {
  previewImageUrl.value = url;
  imagePreviewVisible.value = true;
};

const handleImagePreviewClose = () => {
  imagePreviewVisible.value = false;
};

// 文件数据
const files = ref([
  {
    id: 1,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司21",
    status: "待核对",
	thumbnail: new URL(
	  "@/assets/images/HuameiXingtai_2101.png",
	  import.meta.url
	).href,
	full: new URL("@/assets/images/HuameiXingtai_2101.png", import.meta.url)
	  .href,
	tableData: [
	    { subject: "流动资产111：", current: "", initial: "",  title:"资产负债表11", },
	    {
	      subject: "货币资金",
		  title:"资产负债表11",
	      current: "45,678,921.00",
	      initial: "39,982,145.00",
	    },
	  ]
  },
  {
    id: 2,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "已完成",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
	tableData: [
	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
	    {
		  title:"资产负债表22",
	      subject: "货币资金",
	      current: "45,678,921.00",
	      initial: "39,982,145.00",
	    },
	  ],
  },
  {
    id: 3,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 4,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 5,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 6,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司66",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 7,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 8,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 9,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 10,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  {
    id: 11,
    name: "VD_89736512",
    folder: "深圳市华美兴泰科技股份有限公司33",
    status: "待核对",
  	thumbnail: new URL(
  	  "@/assets/images/gongsi.png",
  	  import.meta.url
  	).href,
  	full: new URL("@/assets/images/gongsi.png", import.meta.url)
  	  .href,
  	tableData: [
  	    { subject: "流动资产2222：", current: "", initial: "", title:"资产负债表22",},
  	    {
  		  title:"资产负债表22",
  	      subject: "货币资金",
  	      current: "45,678,921.00",
  	      initial: "39,982,145.00",
  	    },
  	  ],
  },
  // 其他文件数据...
]);
// 表格数据
const tableData = computed(
  () => files.value[selectedFile.value]?.tableData || []
);

// 表格数据
// const tableData = ref([
//   { subject: "流动资产：", current: "", initial: "", highlight: false },
//   {
//     subject: "货币资金",
//     current: "45,678,921.00",
//     initial: "39,982,145.00",
//     highlight: true,
//   },
//   // 其他数据行...
// ]);

// 状态管理
const searchKeyword = ref("");
const filterStatus = ref("全部");
const selectedFile = ref(0);
const showProblemPanel = ref(false);
const tableTitle = ref("资产负债表");
const problem = ref({
  type: "",
  remark: "",
});

// 计算属性
const currentFile = computed(() => files.value[selectedFile.value] || {});


const filteredFiles = computed(() => {
  return files.value.filter((file) => {
    const matchSearch =
      file.name.includes(searchKeyword.value) ||
      file.folder.includes(searchKeyword.value);
    const matchStatus =
      filterStatus.value === "全部" || file.status === filterStatus.value;
    return matchSearch && matchStatus;
  });
});

// 方法
const statusType = (status) => {
  return status === "待核对" ? "warning" : "success";
};

const selectFile = (index) => {
  selectedFile.value = index;
  // currentFile.image
};

const refreshList = () => {
  console.log("刷新文件列表");
};

const confirmSave = () => {
  console.log("确认保存数据");
};

const addRow = () => {
  tableData.value.push({
    subject: "",
    current: "",
    initial: "",
    highlight: false,
  });
};

const deleteRow = (index) => {
  tableData.value.splice(index, 1);
};

const submitProblem = () => {
  console.log("提交问题:", problem.value);
  showProblemPanel.value = false;
};

const prevFile = () => {
  if (selectedFile.value > 0) selectedFile.value--;
};

const nextFile = () => {
  if (selectedFile.value < files.value.length - 1) selectedFile.value++;
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const paginatedFiles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredFiles.value.slice(start, end);
});

const handleCurrentChange = (val) => {
  currentPage.value = val;
};
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  width: 275px;
}
::v-deep .el-pagination__editor.el-input {
    width: 31px;
    height: 31px;
}
.content-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  .search-filter {
    margin-bottom: 24px;
  }
  .file-panel {
    width: 300px;
    min-width: 300px;
    margin-right: 24px;
    .filter-tabs {
      margin-top: 16px;
    }
  }
  .workspace {
    flex: 1;
    ::v-deep .el-card__body {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .actions {
      margin-left: 24px;
    }
  }
  .grid-container {
    margin-top: 24px;
    display: flex;
    gap: 24px;
    .image-preview,
    .data-editor {
      flex: 1;
    }
	.preview-image {
	  width: 100%;
	  height: 100%;
	}
    .card-header {
      display: flex;
      .header-actions {
        margin-left: auto;
      }
    }
    .editor-content {
      width: 100%;
      .title-input {
        margin-bottom: 16px;
      }
    }
  }
  .footer-actions {
    margin-top: 24px;
    display: flex;
    .navigation {
      margin-left: 400px;
      display: flex;
    }
  }
  .file-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #e8e4e4;
    .file-icon {
      display: inline-block;
      width: 64px;
      height: 64px;
      margin-right: 16px;
      background-color: aquamarine;
      .file-thumbnail {
        width: 100%;
      }
    }
	
    .file-info {
      flex: 1;
      .file-folder {
        font-size: 12px;
        color: #7f8083;
        margin: 6px 0;
      }
    }
  }
  .problem-panel {
    margin-top: 16px;
  }
}
.blue-text {
  color: blue;
}
</style>