import request from '@/utils/request'



// 用户下拉列表状态
export function getDictList(data) {
  return request({
    url: '/system/dict/data/dataList',
    method: 'post',
    data: data
  })
}

// 部门下拉列表
export function getdeptList(data) {
  return request({
    url: '/system/dept/deptTreeData',
    method: 'post',
    data: data
  })
}

// 角色下拉列表
export function getroleList(data) {
  return request({
    url: '/system/role/datas',
    method: 'post',
    data: data
  })
}


//列表查询
export function getData(data){
  return request({
    url: '/system/user/page',
    method: 'post',
    data: data
  })
}

//新增账号
export function addAccount(data){
  return request({
    url: '/system/user/add',
    method: 'post',
    data: data
  })
}
//修改账号

export function editAccount(data){
  return request({
    url: '/system/user/edit',
    method: 'post',
    data: data
  })
}

//禁用重启
export function editStatus(data){
  return request({
    url: '/system/user/status',
    method: 'post',
    data: data
  })
}

//重置密码 
export function resetPassword_(data){
  return request({
    url: '/system/user/resetPassword',
    method: 'post',
    data: data
  })
}

// /biz/sandboxUser/userBindingList  绑定沙箱账号列表
export function userBindingListFn(data){
  return request({
    url: '/biz/sandboxUser/userBindingList',
    method: 'post',
    data: data
  })
}

// ///biz/sandboxUser/list 沙箱账号列表
export function sandboxUserListFn(data){
  return request({
    url: '/biz/sandboxUser/list',
    method: 'post',
    data: data
  })
}



export function sandboxUserDeleteFn(data){
  return request({
    url: '/biz/sandboxUser/delete',
    method: 'post',
    data: data
  })
}


//

export function sandboxUserAddSandBoxUserFn(data){
  return request({
    url: '/biz/sandboxUser/addSandBoxUser',
    method: 'post',
    data: data
  })
}