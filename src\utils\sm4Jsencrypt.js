import { sm4 } from 'gm-crypt'
const pwdKey = 'KWvYCFXchwFM9LSF' //密钥  前后端一致,后端提供
let sm4Config = {
  key: pwdKey, // 这里这个key值要与后端的一致，后端解密是根据这个key
  mode: 'ecb', // 加密的方式有两种，ecb和cbc两种，也是看后端如何定义的
  padding: 'PKCS5Padding', // 填充方式，noPadding表示不填充，PKCS5Padding表示使用PKCS5Padding填充
  cipherType: 'base64'
}

const sm4Util = new sm4(sm4Config) // new一个sm4函数，将上面的sm4Config作为参数传递进去。

/*
 * text 待加密文本
 */
export function encryptsm4(text) {
  if(Object.prototype.toString.call(text) === '[object Object]'){
    text = JSON.stringify(text)
  }
  console.log(text, 'text')
  return sm4Util.encrypt(text, pwdKey)
}

/*
 * text 待解密密文
 */
export function decryptsm4(value) {
  if(Object.prototype.toString.call(value) === '[object Object]'){
    return JSON.parse(sm4Util.decrypt(JSON.stringify(value), pwdKey))
  }
  return JSON.parse(sm4Util.decrypt(value, pwdKey))
}