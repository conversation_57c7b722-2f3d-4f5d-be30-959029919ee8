<template>
  <div class="modeling-history-container">
    <div class="page-header">
      <h1 class="page-title">建模历史</h1>
      
      <!-- 操作按钮 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="primary" class="action-button" @click="showNewModelDialog = true">
            <el-icon class="mr-2"><Plus /></el-icon>
            新建建模
          </el-button>
          <el-button class="action-button" @click="importModel">
            <el-icon class="mr-2"><Upload /></el-icon>
            导入模型
          </el-button>
        </div>
        <div class="right-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索建模记录..."
            class="search-input"
          >
            <template #suffix>
              <el-icon class="cursor-pointer"><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="所属机构">
              <el-select v-model="filters.institution" placeholder="全部" class="w-full" style="width: 100%;">
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in institutionOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="建模状态">
              <el-select v-model="filters.status" placeholder="全部" class="w-full" style="width: 100%;">
                <el-option label="全部" value="" />
                <el-option label="未完成" value="未完成" />
                <el-option label="已完成" value="已完成" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="filters.startDate"
                type="date"
                placeholder="选择日期"
                class="w-full" style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="filters.endDate"
                type="date"
                placeholder="选择日期"
                class="w-full" style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="filter-actions">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="applyFilters">查询</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="paginatedModels" 
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 'bold'
        }"
        :row-style="{ height: '60px' }"
        border
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="id" label="ID" min-width="120" />
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="institution" label="所属机构" min-width="120" />
        <el-table-column prop="sampleSize" label="样本量" width="120" align="right">
          <template #default="{ row }">
            {{ row.sampleSize.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="tableName" label="存储表名" min-width="150" />
        <el-table-column prop="description" label="建模描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="storageLocation" label="模型存储位置" min-width="180" show-overflow-tooltip />
        <el-table-column prop="status" label="建模状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'" effect="light">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button link type="primary" @click="viewModel(row)">查看</el-button>
              <el-button link type="danger" @click="deleteModel(row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalItems"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新建模型对话框 -->
    <el-dialog
      v-model="showNewModelDialog"
      title="新建建模"
      width="600px"
      class="new-model-dialog"
    >
      <el-form
        ref="formRef"
        :model="newModel"
        :rules="rules"
        label-width="120px"
        class="new-model-form"
      >
        <el-form-item label="ID" prop="id">
          <el-input v-model="newModel.id" placeholder="请输入模型ID" />
        </el-form-item>
        <el-form-item label="所属机构" prop="institution">
          <el-select v-model="newModel.institution" placeholder="请选择机构" class="w-full">
            <el-option
              v-for="item in institutionOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="样本量" prop="sampleSize">
          <el-input-number v-model="newModel.sampleSize" :min="1" class="w-full" />
        </el-form-item>
        <el-form-item label="存储表名" prop="tableName">
          <el-input v-model="newModel.tableName" placeholder="请输入存储表名" />
        </el-form-item>
        <el-form-item label="建模描述" prop="description">
          <el-input
            v-model="newModel.description"
            type="textarea"
            :rows="3"
            placeholder="请输入建模描述"
          />
        </el-form-item>
        <el-form-item label="模型存储位置" prop="storageLocation">
          <el-input v-model="newModel.storageLocation" placeholder="请输入模型存储位置" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showNewModelDialog = false">取消</el-button>
          <el-button type="primary" @click="addModel">创建</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Plus, Upload, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 模拟数据源 (通常从API获取)
const allModels = ref([
  { id: 'MODEL001', date: '2024-03-15', institution: '工商银行', sampleSize: 10000, tableName: 'T_MODEL_SAMPLE_001', description: '企业信用评分模型，基于过去三年的财务数据和行为数据，采用逻辑回归算法。', storageLocation: '/models/production/credit_score_v1.2', status: '已完成' },
  { id: 'MODEL002', date: '2024-03-16', institution: '建设银行', sampleSize: 15000, tableName: 'T_MODEL_SAMPLE_002', description: '企业风险评估模型，主要用于中小企业的贷款审批，包含多种风险因子。', storageLocation: '/models/development/risk_assessment_v0.9', status: '未完成' },
  { id: 'MODEL003', date: '2024-03-17', institution: '农业银行', sampleSize: 20000, tableName: 'T_MODEL_SAMPLE_003', description: '企业欺诈检测模型，结合交易数据和用户画像，实时监测异常行为。', storageLocation: '/models/production/fraud_detection_v2.0', status: '已完成' },
  { id: 'MODEL004', date: '2024-03-18', institution: '中国银行', sampleSize: 18000, tableName: 'T_MODEL_SAMPLE_004', description: '中小企业贷款风险模型，新版本迭代中，增加了更多行业特定变量。', storageLocation: '/models/staging/loan_risk_v1.5_beta', status: '已完成' },
  { id: 'MODEL005', date: '2024-03-19', institution: '交通银行', sampleSize: 12000, tableName: 'T_MODEL_SAMPLE_005', description: '企业破产预测模型，基于深度学习方法，对高危企业提前预警。', storageLocation: '/models/development/bankruptcy_prediction_v0.3', status: '未完成' },
  { id: 'MODEL006', date: '2024-04-01', institution: '招商银行', sampleSize: 25000, tableName: 'T_MODEL_SAMPLE_006', description: '零售客户信用分层模型', storageLocation: '/models/production/retail_credit_tier_v1.0', status: '已完成' },
  { id: 'MODEL007', date: '2024-04-05', institution: '工商银行', sampleSize: 9000, tableName: 'T_MODEL_SAMPLE_007', description: '信用卡申请反欺诈模型', storageLocation: '/models/production/cc_antifraud_v3.1', status: '已完成' },
  { id: 'MODEL008', date: '2024-04-10', institution: '建设银行', sampleSize: 16000, tableName: 'T_MODEL_SAMPLE_008', description: '供应链金融风险评估', storageLocation: '/models/development/supply_chain_finance_risk_v0.1', status: '未完成' },
  { id: 'MODEL009', date: '2024-04-15', institution: '农业银行', sampleSize: 22000, tableName: 'T_MODEL_SAMPLE_009', description: '精准营销响应模型', storageLocation: '/models/production/precision_marketing_v1.0', status: '已完成' },
  { id: 'MODEL010', date: '2024-04-20', institution: '中国银行', sampleSize: 13000, tableName: 'T_MODEL_SAMPLE_010', description: '外汇交易风险监控模型', storageLocation: '/models/staging/fx_trade_risk_v0.8', status: '已完成' },
  { id: 'MODEL011', date: '2024-04-25', institution: '交通银行', sampleSize: 11000, tableName: 'T_MODEL_SAMPLE_011', description: '操作风险量化模型', storageLocation: '/models/development/operational_risk_quant_v0.2', status: '未完成' },
  { id: 'MODEL012', date: '2024-05-01', institution: '招商银行', sampleSize: 30000, tableName: 'T_MODEL_SAMPLE_012', description: '财富管理客户流失预警模型', storageLocation: '/models/production/wealth_churn_v1.1', status: '已完成' },
]);

// 机构选项
const institutionOptions = ['工商银行', '建设银行', '农业银行', '中国银行', '交通银行', '招商银行', '浦发银行', '中信银行'];

// 搜索和筛选
const searchQuery = ref('');
const filters = reactive({
  institution: '',
  status: '',
  startDate: '',
  endDate: ''
});

const applyFilters = () => {
  currentPage.value = 1; // 每次应用筛选时回到第一页
  // filteredModels会根据filters自动更新
};

const resetFilters = () => {
  filters.institution = '';
  filters.status = '';
  filters.startDate = '';
  filters.endDate = '';
  searchQuery.value = ''; // 也重置搜索词
  currentPage.value = 1;
};


// 过滤后的模型数据
const filteredModels = computed(() => {
  let modelsToFilter = [...allModels.value];

  // 搜索
  if (searchQuery.value.trim()) {
    const lowerSearchQuery = searchQuery.value.toLowerCase();
    modelsToFilter = modelsToFilter.filter(model =>
      model.id.toLowerCase().includes(lowerSearchQuery) ||
      model.description.toLowerCase().includes(lowerSearchQuery) ||
      model.tableName.toLowerCase().includes(lowerSearchQuery) ||
      model.institution.toLowerCase().includes(lowerSearchQuery)
    );
  }

  // 机构筛选
  if (filters.institution) {
    modelsToFilter = modelsToFilter.filter(model => model.institution === filters.institution);
  }
  
  // 状态筛选
  if (filters.status) {
    modelsToFilter = modelsToFilter.filter(model => model.status === filters.status);
  }
  
  // 日期筛选
  if (filters.startDate) {
    modelsToFilter = modelsToFilter.filter(model => new Date(model.date) >= new Date(filters.startDate));
  }
  if (filters.endDate) {
    modelsToFilter = modelsToFilter.filter(model => new Date(model.date) <= new Date(filters.endDate));
  }
  
  return modelsToFilter.sort((a, b) => new Date(b.date) - new Date(a.date)); // 按日期降序排序
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10); // 每页显示条目数
const totalItems = computed(() => filteredModels.value.length);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 当前页显示的模型数据
const paginatedModels = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredModels.value.slice(start, end);
});


// 计算分页显示的页码 (简化版，可根据需要扩展为更复杂的如带省略号的分页)
const paginationItems = computed(() => {
  const pages = [];
  const maxVisiblePages = 5; // 最多显示的页码按钮数
  let startPage = 1;
  let endPage = totalPages.value;

  if (totalPages.value > maxVisiblePages) {
    startPage = Math.max(currentPage.value - Math.floor(maxVisiblePages / 2), 1);
    endPage = startPage + maxVisiblePages - 1;
    if (endPage > totalPages.value) {
      endPage = totalPages.value;
      startPage = Math.max(endPage - maxVisiblePages + 1, 1);
    }
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  return pages;
});

// 跳转到指定页
const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 新建模型相关
const showNewModelDialog = ref(false);
const initialNewModel = () => ({
  id: '',
  institution: '',
  sampleSize: null,
  tableName: '',
  description: '',
  storageLocation: '',
});
const newModel = reactive(initialNewModel());

const resetNewModelForm = () => {
  Object.assign(newModel, initialNewModel());
};

// 查看模型详情 (模拟)
const viewModel = (model) => {
  ElMessage({
    message: `查看模型详情:
ID: ${model.id}
机构: ${model.institution}
描述: ${model.description}
状态: ${model.status}`,
    type: 'info',
    duration: 5000,
  })
};

// 删除模型
const deleteModel = (modelId) => {
  ElMessageBox.confirm(
    `确定要删除模型 "${modelId}" 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = allModels.value.findIndex(m => m.id === modelId)
    if (index !== -1) {
      allModels.value.splice(index, 1)
      if (paginatedModels.value.length === 0 && currentPage.value > 1) {
        currentPage.value--
      }
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
};

// 导入模型 (模拟)
const importModel = () => {
  alert('导入模型功能待实现');
};

// 添加新模型
const addModel = () => {
  // 简单验证
  if (!newModel.id || !newModel.tableName || !newModel.institution || !newModel.sampleSize) {
    alert('请填写所有必填项（带*号）');
    return;
  }
   if (allModels.value.some(m => m.id === newModel.id)) {
    alert('模型ID已存在，请输入唯一的ID。');
    return;
  }

  const modelToAdd = {
    ...newModel,
    date: new Date().toISOString().split('T')[0], // 当前日期
    status: '未完成' // 默认状态
  };
  
  allModels.value.unshift(modelToAdd); // 添加到列表开头
  
  resetNewModelForm();
  showNewModelDialog.value = false;
  currentPage.value = 1; // 添加后跳转到第一页查看新数据
};

// 模拟API请求 (在实际应用中替换为真实API调用)
const fetchModels = async () => {
  console.log('正在获取模型数据...');
  // 模拟网络延迟
  // await new Promise(resolve => setTimeout(resolve, 1000));
  // 在实际应用中，这里会是:
  // try {
  //   const response = await fetch('/api/modeling-history');
  //   if (!response.ok) throw new Error('Network response was not ok');
  //   allModels.value = await response.json();
  // } catch (error) {
  //   console.error('获取模型数据失败:', error);
  //   //  可以设置一个错误状态供UI显示
  // }
  console.log('模型数据已加载 (模拟)');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchModels();
});

// 表单验证规则
const rules = {
  id: [
    { required: true, message: '请输入ID', trigger: 'blur' },
  ],
  institution: [
    { required: true, message: '请选择所属机构', trigger: 'change' },
  ],
  sampleSize: [
    { required: true, message: '请输入样本量', trigger: 'blur' },
  ],
  tableName: [
    { required: true, message: '请输入存储表名', trigger: 'blur' },
  ],
}

// 分页方法
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}
</script>

<style scoped>
.modeling-history-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.left-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
}

.search-input {
  width: 280px;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.filter-form {
  padding: 8px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.table-card {
  border-radius: 8px;
}

.table-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.new-model-dialog :deep(.el-dialog__body) {
  padding: 20px 40px;
}

.new-model-form {
  max-width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

:deep(.el-card) {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}
</style> 