import request from '@/utils/request'



// 角色列表
export function getRoleList(data) {
  return request({
    url: '/system/role/page',
    method: 'post',
    data: data
  })
}

//新增角色
export function saveRole(data) {
  return request({
    url: '/system/role/save',
    method: 'post',
    data: data
  })
}

//启用 /system/role/status
export function saveStatusRole(data) {
  return request({
    url: '/system/role/status',
    method: 'post',
    data: data
  })
}

// 查功能权限配置 /system/menu/roleMenuTreeData
export function roleMenuTreeDataFn(data) {
  return request({
    url: '/system/menu/roleMenuTreeData',
    method: 'post',
    data: data
  })
}

//保存功能权限配置 /system/role/function
export function saveRoleMenuTreeDataFn(data) {
  return request({
    url: '/system/role/function',
    method: 'post',
    data: data
  })
}

//编辑角色
export function editRoleFn(data) {
  return request({
    url: '/system/role/edit',
    method: 'post',
    data: data
  })
}

//del角色
export function delRoleFn(data) {
  return request({
    url: '/system/role/remove',
    method: 'post',
    data: data
  })
}