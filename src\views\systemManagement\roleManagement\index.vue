<template>
    <div class="role-management p-6 bg-gray-50 min-h-screen memory-alert">
        <h1 class="text-2xl font-bold text-gray-800">角色管理</h1>
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between mb-8">

                <div class="flex items-center gap-4"
                    style="display: flex; flex-direction: row; justify-content: space-between; margin-bottom: 24px;">

                    <div>
                        <el-button type="primary" @click="showNewRoleDialog = true" class="flex items-center"
                            v-hasPermi="['system:role:add']">
                            <el-icon class="mr-2">
                                <Plus />
                            </el-icon>
                            新建角色
                        </el-button>
                    </div>

                    <div style="width: 300px;">
                        <el-input v-model="searchQuery" placeholder="搜索角色..." class="w-64" clearable
                            @keyup.enter="applyFilters">
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <el-card class="mb-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                <el-table :data="filteredRoles" style="width: 100%" v-loading="loading" :header-cell-style="{
                    background: '#f8fafc',
                    color: '#1e293b',
                    fontWeight: '600'
                }" :cell-style="{
                    padding: '16px 0'
                }">
                    <el-table-column type="index" label="序号" width="80" align="center" />
                    <el-table-column prop="roleName" label="角色名称" min-width="120" />
                    <el-table-column prop="remark" label="角色描述" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="createTime" label="创建时间" width="160" />
                    <el-table-column prop="count" label="用户数量" width="100" align="center" />
                    <el-table-column prop="statusStr" label="状态" width="100" align="center">
                        <template #default="{ row }">
                            <el-tag :type="row.statusStr === '启用' ? 'success' : 'danger'" effect="light"
                                class="px-3 py-1">
                                {{ row.statusStr }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="400" fixed="right">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center gap-2">
                                <el-button v-if="row.roleName != '超级管理员'" type="primary" link @click="editRole(row)"
                                    v-hasPermi="['system:role:edit']">编辑</el-button>
                                <a disabled v-else>编辑</a>
                                <el-divider direction="vertical" v-hasPermi="['system:role:edit']" />

                                <el-button v-if="row.roleName != '超级管理员'" type="primary" link @click="delRole(row)"
                                    v-hasPermi="['system:role:delete']">删除</el-button>
                                <a disabled v-else>删除</a>
                                <el-divider direction="vertical" v-hasPermi="['system:role:edit']" />


                                <el-button v-if="row.roleName != '超级管理员'" type="primary" link
                                    @click="configPermissions(row)"
                                    v-hasPermi="['system:role:funcPermPage']">功能权限配置</el-button>
                                <a disabled v-else>功能权限配置</a>
                                <el-divider direction="vertical" v-hasPermi="['system:role:funcPermPage']" />

                                <el-button v-if="row.roleName != '超级管理员'" type="primary" link
                                    @click="configDataPermissions(row)"
                                    v-hasPermi="['system:role:dataPermPage']">数据权限配置</el-button>
                                <a disabled v-else>数据权限配置</a>
                                <el-divider direction="vertical" v-hasPermi="['system:role:dataPermPage']" />

                                <el-button v-if="row.roleName != '超级管理员'"
                                    :type="row.statusStr === '启用' ? 'danger' : 'success'" link
                                    @click="toggleRoleStatus(row)">
                                    {{ row.status === 0 ? '禁用' : '启用' }}
                                </el-button v-hasPermi="['system:role:status']">
                                <a disabled v-else> {{ row.status === 0 ? '禁用' : '启用' }}</a>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="mt-6 flex justify-end">
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                        :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        class="custom-pagination" />
                </div>

            </el-card>
        </div>

        <!-- 新建角色对话框 -->
        <el-dialog v-model="showNewRoleDialog" :rules="rules" title="新建角色" width="500px" destroy-on-close
            class="role-dialog" @close='cancleAddFn'>
            <el-form :model="newRole" :rules="rules" label-width="100px" class="px-4" ref="newForm">
                <el-form-item label="角色名称" prop="name">
                    <el-input v-model="newRole.name" placeholder="请输入角色名称" />
                </el-form-item>
                <el-form-item label="角色描述">
                    <el-input v-model="newRole.description" type="textarea" rows="3" placeholder="请输入角色描述" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="newRole.status">
                        <el-radio label="启用" value="0"></el-radio>
                        <el-radio label="禁用" value="1"></el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancleAddFn">取消</el-button>
                    <el-button type="primary" @click="addRole">保存</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 编辑角色对话框 -->
        <el-dialog v-model="showEditRoleDialog" title="编辑角色" width="500px" destroy-on-close class="role-dialog">
            <el-form :model="editingRole" :rules="rules2" label-width="100px" class="px-4" ref="editForm">
                <el-form-item label="角色名称" prop="name">
                    <el-input v-model="editingRole.name" placeholder="请输入角色名称" />
                </el-form-item>
                <el-form-item label="角色描述">
                    <el-input v-model="editingRole.description" type="textarea" rows="3" placeholder="请输入角色描述" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="editingRole.status">
                        <el-radio label="启用" value="0"></el-radio>
                        <el-radio label="禁用" value="1"></el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showEditRoleDialog = false">取消</el-button>
                    <el-button type="primary" @click="updateRole">保存</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 权限配置对话框 -->
        <el-dialog v-model="showPermissionsDialog" :title="`权限配置 - ${currentRole?.roleName}`" width="800px"
            destroy-on-close class="permissions-dialog">
            <div class="max-h-[60vh] overflow-y-auto px-4">
                <el-tree ref="permissionTree" :data="listToTrees" show-checkbox node-key="id" :props="{
                    label: 'name',
                    children: 'children'
                }" :default-checked-keys="checkedKeys" :default-expanded-keys="checkedKeys" :check-strictly="false"
                    @check="handleCheck" class="permission-tree">
                    <template #default="{ node, data }">
                        <span class="custom-tree-node">
                            <span>{{ node.label }}</span>
                        </span>
                    </template>
                </el-tree>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showPermissionsDialog = false">取消</el-button>
                    <el-button type="primary" @click="savePermissions">保存</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { Plus, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { saveRole, getRoleList, editRoleFn, saveRoleMenuTreeDataFn, saveStatusRole, roleMenuTreeDataFn, delRoleFn } from '@/api/systemManagement/roleManagement'
// 模拟数据 - 角色列表
const roles = ref();

// 表单验证规则
const rules = {
    status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
}
const rules2 = {
    status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
}
const newForm = ref(null)
const editForm = ref(null)
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0)
// 搜索
const searchQuery = ref('');
const loading = ref(false);

// 对话框控制
const showNewRoleDialog = ref(false);
const showEditRoleDialog = ref(false);
const showPermissionsDialog = ref(false);
const oneModule = ref([])
const twoModule = ref([])
const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchRolesData()
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchRolesData()
};

// 表单数据
const newRole = reactive({
    name: '',
    description: '',
    status: 0
});

const editingRole = reactive({
    id: null,
    name: '',
    description: '',
    status: '启用'
});

const currentRole = ref(null);
const selectedPermissions = ref([1, 2, 3, 5, 9, 14, 19]); // 模拟已选权限

// 筛选角色
const filteredRoles = ref([])

// 添加角色
const addRole = async () => {
    // if (!newRole.name) {
    //     ElMessage.warning('请输入角色名称');
    //     return;
    // }
    await newForm.value.validate(async (valid) => {
        console.log(111)
        if (valid) {


            let params = {
                roleName: newRole.name,
                remark: newRole.description,
                status: newRole.status == '启用' ? 0 : (newRole.status == '禁用' ? 1 : '')
            }

            saveRole(params).then(res => {
                if (res.code == 200) {
                    ElMessage.success('角色创建成功');
                    fetchRolesData()
                    newRole.name = ''
                    newRole.description = ''
                    newRole.status = ''
                    showNewRoleDialog.value = false
                }
                console.log(res, 'saveRole')
            }).catch(err => {
                console.log(err, 'err')
            })
        }
    });


    //   const roleToAdd = {
    //     ...newRole,
    //     id: Math.max(...roles.value.map(r => r.id)) + 1,
    //     createdAt: new Date().toISOString().replace('T', ' ').substring(0, 19),
    //     userCount: 0
    //   };

    //   roles.value.push(roleToAdd);

    //   // 重置表单并关闭对话框
    //   newRole.name = '';
    //   newRole.description = '';
    //   newRole.status = '启用';
    //   showNewRoleDialog.value = false;


};

// 编辑角色
const editRole = (role) => {

    editingRole.id = role.roleId
    editingRole.name = role.roleName
    editingRole.description = role.remark
    editingRole.status = role.statusStr

    showEditRoleDialog.value = true;
};


const cancleAddFn = () => {
    newRole.name = ''
    newRole.description = ''
    newRole.status = ''
    showNewRoleDialog.value = false
}




// Convert flat array to tree structure
const convertToTree = (data) => {
    // Create a map to store all items by their id
    const itemMap = new Map();

    // First pass: create map of all items
    data.forEach(item => {
        itemMap.set(item.id, { ...item, children: [] });
    });

    // Second pass: build tree structure
    const tree = [];
    data.forEach(item => {
        const node = itemMap.get(item.id);
        if (item.pId === 0) {
            // Root level items
            tree.push(node);
        } else {
            // Child items
            const parent = itemMap.get(item.pId);
            if (parent) {
                parent.children.push(node);
            }
        }
    });

    return tree;
};


// 更新角色
const updateRole = async () => {
    // if (!editingRole.name) {
    //     ElMessage.warning('请输入角色名称');
    //     return;
    // }

    await editForm.value.validate(async (valid) => {
        if (valid) {

            let params = {
                roleName: editingRole.name,
                remark: editingRole.description,
                status: editingRole.status == '启用' ? 0 : (editingRole.status == '禁用' ? 1 : ''),
                roleId: editingRole.id
            }
            //   const index = roles.value.findIndex(r => r.id === editingRole.id);
            //   if (index !== -1) {
            //     roles.value[index] = { ...editingRole };
            //   }


            editRoleFn(params).then(res => {
                if (res.code == 200) {
                    editingRole.id = ''
                    editingRole.name = ''
                    editingRole.description = ''
                    editingRole.status = ''
                    showEditRoleDialog.value = false;
                    ElMessage.success('角色更新成功');
                    fetchRolesData()
                }
            }).catch(err => {
                console.log(err, 'err')
            })

        }
    });




};

// 切换角色状态
const toggleRoleStatus = (row) => {
    if (row.roleId) {
        const newStatus = row.status === 0 ? '禁用' : '启用';

        ElMessageBox.confirm(
            `确定要${newStatus}角色"${row.roleName}"吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            saveStatusRole({
                status: row.status === 0 ? 1 : 0,
                roleId: row.roleId
            }).then(res => {
                if (res.code == 200) {
                    fetchRolesData();
                    ElMessage.success(`角色已${action}`);
                }
            })


        }).catch(() => { });
    }
};


// 切换角色状态
const delRole = (row) => {

    ElMessageBox.confirm(
        `确定要删除吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        delRoleFn({
            roleId: row.roleId
        }).then(res => {
            if (res.code == 200) {
                fetchRolesData();
                ElMessage.success(`删除成功!`);
            }
        })


    }).catch(() => { });

};

let listToTrees = ref([])
const isFlag = ref(true)
const permissionTree = ref(null);
const checkedKeys = ref([]);


// 获取所有“全选”节点（所有子节点都被选中才算全选）
function getFullyCheckedKeys(nodes) {
    let checked = [];
    nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
            const childChecked = getFullyCheckedKeys(node.children);
            // 如果所有子节点都被选中，父节点才被选中
            if (childChecked.length === node.children.length && node.children.every(child => child.checked)) {
                checked.push(node.id);
            }
            checked = checked.concat(childChecked);
        } else {
            if (node.checked) checked.push(node.id);
        }
    });
    return checked;
}

// 打开权限配置
const configPermissions = (role) => {
    currentRole.value = { ...role };
    roleMenuTreeDataFn({
        roleId: role.roleId
    }).then(res => {
        console.log("权限菜单res:", res);

        // 处理数据，确保每个节点都有 checked 属性
        const processData = (nodes) => {
            return nodes.map(node => ({
                ...node,
                checked: Boolean(node.checked),  // 确保 checked 是布尔值
                children: node.children ? processData(node.children) : []
            }));
        };

        // 先清空之前的数据
        listToTrees.value = [];
        checkedKeys.value = [];

        // 设置新的数据
        listToTrees.value = processData(convertToTree(res.data));
        checkedKeys.value = getFullyCheckedKeys(listToTrees.value);

        // // 获取已选中的节点
        // const getCheckedNodes = (nodes) => {
        //     let checked = [];
        //     nodes.forEach(node => {
        //         if (node.checked == true) {  // 只处理明确为 true 的节点
        //             checked.push(node.id);
        //             if (node.children && node.children.length > 0) {
        //                 checked = checked.concat(getCheckedNodes(node.children));
        //             }
        //         }
        //     });
        //     return checked;
        // };

        // checkedKeys.value = getCheckedNodes(listToTrees.value);
        console.log('选中的节点:', checkedKeys.value);

        // 在下一个 tick 设置选中状态
        nextTick(() => {
            if (permissionTree.value) {
                // 先清空所有选中状态
                permissionTree.value.setCheckedKeys([]);
                // 再设置选中的节点
                if (checkedKeys.value.length > 0) {
                    permissionTree.value.setCheckedKeys(checkedKeys.value);
                }
            } else {
                console.warn('权限树组件未初始化');
            }
        });

        showPermissionsDialog.value = true;
    });
};

const qu_xiao_val = ref([])

// 修改 handleCheck 函数
const handleCheck = (data, { checkedKeys: keys, checkedNodes, halfCheckedKeys }) => {
    console.log('选中的节点:', keys);
    console.log('半选中的节点:', halfCheckedKeys);

    // 如果当前节点是一级菜单且被取消勾选
    if (data.pId === 0 && !keys.includes(data.id)) {
        // qu_xiao_val.value = [...qu_xiao_val.value, data.id]
        // qu_xiao_val.value.push(data.id)
        // 递归取消所有子节点
        const uncheckChildren = (node) => {
            if (node.children && node.children.length > 0) {
                node.children.forEach(child => {
                    permissionTree.value.setChecked(child.id, false, true); // true 表示递归
                    // qu_xiao_val.value = [...qu_xiao_val.value, child.id]
                    uncheckChildren(child);
                });
            }
        };
        uncheckChildren(data);
    }

    // 更新节点的选中状态
    const updateNodeChecked = (nodes, checkedIds, halfCheckedIds) => {
        nodes.forEach(node => {
            if (node.children && node.children.length > 0) {
                // 先处理子节点
                updateNodeChecked(node.children, checkedIds, halfCheckedIds);

                // 检查子节点的选中状态
                const allChildrenChecked = node.children.every(child => child.checked === true);
                const someChildrenChecked = node.children.some(child => child.checked === true);

                // 设置父节点的选中状态
                if (allChildrenChecked) {
                    node.checked = true;
                } else if (someChildrenChecked || halfCheckedIds.includes(node.id)) {
                    node.checked = 'indeterminate';
                } else {
                    node.checked = false;
                }
            } else {
                // 叶子节点直接根据 checkedIds 设置状态
                node.checked = checkedIds.includes(node.id);
            }
        });
    };



    updateNodeChecked(listToTrees.value, keys, halfCheckedKeys);
    console.log('更新后的选中状态:', listToTrees.value);
};

// 修改保存权限配置函数
const savePermissions = () => {
    ElMessageBox.confirm(
        `确定要保存对角色"${currentRole.value.roleName}"的权限配置吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        const checkedNodes = permissionTree.value.getCheckedKeys();
        const halfCheckedNodes = permissionTree.value.getHalfCheckedKeys();
        const allCheckedNodes = [...checkedNodes, ...halfCheckedNodes];
        console.log('permissionTree.value.getCheckedKeys()',permissionTree.value.getCheckedKeys())
        let params = {
            // menuIds: allCheckedNodes,
            menuIds: allCheckedNodes,
            roleId: currentRole.value.roleId
        };

        console.log("保存权限入参params:", params);

        saveRoleMenuTreeDataFn(params).then(res => {
            if (res.code === 200) {
                // 更新本地数据状态
                const updateNodeChecked = (nodes, checkedIds, halfCheckedIds) => {
                    nodes.forEach(node => {
                        if (node.children && node.children.length > 0) {
                            // 先处理子节点
                            updateNodeChecked(node.children, checkedIds, halfCheckedIds);

                            // 检查子节点的选中状态
                            const allChildrenChecked = node.children.every(child => child.checked === true);
                            const someChildrenChecked = node.children.some(child => child.checked === true);

                            // 设置父节点的选中状态
                            if (allChildrenChecked) {
                                node.checked = true;
                            } else if (someChildrenChecked || halfCheckedIds.includes(node.id)) {
                                node.checked = 'indeterminate';
                            } else {
                                node.checked = false;
                            }
                        } else {
                            // 叶子节点直接根据 checkedIds 设置状态
                            node.checked = checkedIds.includes(node.id);
                        }
                    });
                };
                updateNodeChecked(listToTrees.value, checkedNodes, halfCheckedNodes);

                ElMessage.success('权限配置已保存');
                showPermissionsDialog.value = false;
            } else {
                ElMessage.error(res.msg || '保存失败');
            }
        }).catch((err) => {
            console.log(err, 'err');
            ElMessage.error('保存失败');
        });
    }).catch(() => { });
};

// 打开数据权限配置
const configDataPermissions = (role) => {
    router.push({
        path: '/roleManagement/dataDetail',
        query: {
            row: encodeURIComponent(JSON.stringify(role))
        }
    });
};


// 获取角色数据
const fetchRolesData = async () => {
    loading.value = true;
    try {
        console.log('获取角色数据...');
        let params = {
            roleName: searchQuery.value,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }
        getRoleList(params).then(res => {
            if (res.code == 200) {
                filteredRoles.value = res.rows
                total.value = res.total
                loading.value = false;
            }
            console.log(res)
        })
        // 在实际应用中应调用API
        // roles.value = await api.getRoles();
    } finally {

    }
};

const applyFilters = () => {
    currentPage.value = 1;
    fetchRolesData()
}

const router = useRouter();

onMounted(() => {
    fetchRolesData();
});
</script>

<style scoped>
.role-management {
    background-color: #f8fafc;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
}

:deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

:deep(.el-dialog__body) {
    padding: 24px 24px;
}

:deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table) {
    --el-table-border-color: #e5e7eb;
    --el-table-header-bg-color: #f8fafc;
}

:deep(.el-table th) {
    font-weight: 600;
}

:deep(.el-checkbox-group) {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

:deep(.el-checkbox) {
    margin-right: 0;
    height: 32px;
    display: flex;
    align-items: center;
}

.memory-alert {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.custom-pagination {
    --el-pagination-button-bg-color: #fff;
    --el-pagination-hover-color: #409eff;
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
}

::v-deep .el-dialog__body {
    max-height: 600px;
    overflow: auto;
}

.permission-tree {
    padding: 10px;
}

.permission-tree :deep(.el-tree-node__content) {
    height: 40px;
}

.permission-tree :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
}

/* 选中状态样式 */
.permission-tree :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #409eff;
    border-color: #409eff;
}

/* 未选中状态样式 */
.permission-tree :deep(.el-checkbox__input .el-checkbox__inner) {
    background-color: #fff;
    border-color: #dcdfe6;
}

/* 半选状态样式 */
.permission-tree :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: #409eff;
    border-color: #409eff;
}

/* 悬停状态样式 */
.permission-tree :deep(.el-checkbox__input:hover .el-checkbox__inner) {
    border-color: #409eff;
}

/* 当前节点样式 */
.permission-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #f5f7fa;
}

/* 确保未选中状态的样式优先级 */
.permission-tree :deep(.el-checkbox__input:not(.is-checked):not(.is-indeterminate) .el-checkbox__inner) {
    background-color: #fff !important;
    border-color: #dcdfe6 !important;
}

/* 确保未选中状态的勾选图标不显示 */
.permission-tree :deep(.el-checkbox__input:not(.is-checked):not(.is-indeterminate) .el-checkbox__inner::after) {
    display: none !important;
}

/* 确保未选中状态的复选框样式 */
.permission-tree :deep(.el-checkbox__input:not(.is-checked):not(.is-indeterminate)) {
    .el-checkbox__inner {
        background-color: #fff !important;
        border-color: #dcdfe6 !important;

        &::after {
            display: none !important;
        }
    }
}

/* 半选状态样式 */
.permission-tree :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: #409eff;
    border-color: #409eff;
}

.permission-tree :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner::after) {
    content: '';
    position: absolute;
    display: block;
    background-color: #fff;
    height: 2px;
    transform: scale(.5);
    left: 0;
    right: 0;
    top: 5px;
}

/* 确保半选状态的样式优先级 */
.permission-tree :deep(.el-checkbox__input.is-indeterminate) {
    .el-checkbox__inner {
        background-color: #409eff !important;
        border-color: #409eff !important;
    }
}

a {
    color: #a8abb2;
    cursor: default;
}
</style>