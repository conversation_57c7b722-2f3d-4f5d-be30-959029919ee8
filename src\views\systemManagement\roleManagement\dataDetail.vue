<script setup>
    import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue';
    import { useRoute, useRouter } from 'vue-router';
    import { ElMessage, ElTable, ElTableColumn, ElPagination, ElSelect, ElOption, ElInput, ElButton, ElCheckbox } from 'element-plus';
    import {
        getDataList,
        roleVarDictPage,
        roleVarDictEdit
    } from '@/api/featureSelection/variableDictionary.js'
    import {

        getfirstDimensionOptions,
        getsecondDimensionOptions,
        getthirdDimensionOptions
    } from '@/api/featureSelection/variableDictionary.js'

    const route = useRoute();
    const router = useRouter();

    const isQuanXuanFlag = ref(false)
    const isLoadingData = ref(false) // 标记是否正在加载数据

    // 加载状态
    const loading = ref(false);

    // 筛选条件
    const filterForm = reactive({
        firstDimension: '',
        secondDimension: '',
        thirdDimension: '',
        variableType: '',
        cnName: '',
        enName: ''
    });
    const multipleTable = ref()
    // 一级维度选项
    const firstDimensionOptions = ref([]);

    // 二级维度选项
    const secondDimensionOptions = ref([]);

    // 三级维度选项
    const thirdDimensionOptions = ref([]);

    // 变量类型选项
    const variableTypeOptions = ref([]);

    // 表格数据
    const tableData = ref([]);
    const selectedVariables = ref([]);

    const multipleSelection = ref([])


    // 分页
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    const obj = ref({})
    const handleSizeChange = (val) => {
        console.log('分页大小变化，当前全选状态:', isQuanXuanFlag.value)
        pageSize.value = val;
        currentPage.value = 1;
        // 只有在用户主动操作过全选按钮时才保存状态
        const currentSavedFlag = localStorage.getItem('isQuanXuanFlag')
        if (currentSavedFlag !== null) {
            localStorage.setItem('isQuanXuanFlag', JSON.stringify(isQuanXuanFlag.value))
            console.log('分页大小变化后，全选状态已保存:', isQuanXuanFlag.value)
        } else {
            console.log('分页大小变化，保持默认状态（未操作过全选）')
        }
        isLoadingData.value = true // 标记开始加载数据
        fetchVariables()
    };

    const handleCurrentChange = (val) => {
        console.log('页码变化，当前全选状态:', isQuanXuanFlag.value)
        currentPage.value = val;
        // 只有在用户主动操作过全选按钮时才保存状态
        const currentSavedFlag = localStorage.getItem('isQuanXuanFlag')
        if (currentSavedFlag !== null) {
            localStorage.setItem('isQuanXuanFlag', JSON.stringify(isQuanXuanFlag.value))
            console.log('页码变化后，全选状态已保存:', isQuanXuanFlag.value)
        } else {
            console.log('页码变化，保持默认状态（未操作过全选）')
        }
        isLoadingData.value = true // 标记开始加载数据
        fetchVariables()
    };



    // 查询方法
    const handleSearch = async () => {
        loading.value = true;
        try {
            total.value = [];
            currentPage.value = 1;
        } catch (error) {
            console.error('筛选变量失败:', error);
        } finally {
            loading.value = false;
        }
    };

    // 重置方法
    const handleReset = () => {
        filterForm.firstDimension = '';
        filterForm.secondDimension = '';
        filterForm.thirdDimension = '';
        filterForm.variableType = '';
        filterForm.cnName = '';
        filterForm.enName = '';
        multipleTable.value.clearSelection()
        isQuanXuanFlag.value = false
        // 清除本地存储的全选状态
        localStorage.removeItem('isQuanXuanFlag')
        localStorage.removeItem('invert_ids')
        localStorage.removeItem('danqian_selectItems')
        handleSearch();
    };

    // 保存数据权限配置
    const saveDataPermissions = async () => {
        try {

            console.log(JSON.parse(localStorage.getItem('invert_ids')))
            console.log(JSON.parse(localStorage.getItem('danqian_selectItems')))

            let invert_ids = JSON.parse(localStorage.getItem('invert_ids')) || []
            let danqian_selectItems = JSON.parse(localStorage.getItem('danqian_selectItems')) || []
            let savedQuanXuanFlag = localStorage.getItem('isQuanXuanFlag')

            let params = {}

            if (isQuanXuanFlag.value) {
                // 用户点击了全选
                params = {
                    roleId: obj.value.roleId,
                    selectList: [],
                    unSelectList: invert_ids,
                    selectAll: 1
                }
                console.log('保存参数：全选状态', params)
            } else if (savedQuanXuanFlag === 'false') {
                // 用户主动取消了全选，清空所有勾选状态
                params = {
                    roleId: obj.value.roleId,
                    selectList: [],
                    unSelectList: [],
                    selectAll: 0,
                    clearAll: true  // 添加标识表示清空所有选择
                }
                console.log('保存参数：取消全选状态，清空所有勾选', params)
            } else {
                // 默认状态或单独选择状态
                params = {
                    roleId: obj.value.roleId,
                    selectList: danqian_selectItems,
                    unSelectList: [],
                    selectAll: 0
                }
                console.log('保存参数：单独选择状态', params)
            }
            // return
            // return

            roleVarDictEdit(params).then(res => {
                if (res.code == 200) {
                    console.log(res, 'res')
                    ElMessage.success('数据权限配置已保存');

                    // 清除所有本地选择状态，让系统重新根据后端checked字段决定勾选状态
                    localStorage.removeItem('pre_selectItems')
                    localStorage.removeItem('danqian_selectItems')
                    localStorage.removeItem('invert_ids')
                    localStorage.removeItem('isQuanXuanFlag')

                    // 重置全选状态
                    isQuanXuanFlag.value = false

                    console.log('保存成功，已清除所有本地选择状态，将根据后端checked字段重新勾选')

                    setTimeout(() => {
                        fetchVariables()
                    }, 100)
                }
            })
            // 在实际应用中，这里应该调用API保存权限
            // await api.saveRoleDataPermissions(roleInfo.id, selectedVariables.value);

            // router.push('/role-management');
        } catch (error) {
            console.error('保存数据权限失败:', error);
            ElMessage.error('保存失败，请重试');
        }
    };



    // 获取所有变量
    const fetchVariables = async () => {
        loading.value = true;
        try {
            obj.value = JSON.parse(decodeURIComponent(route.query.row))
            let params = {
                firstDimension: filterForm.firstDimension,
                secondDimension: filterForm.secondDimension,
                thirdDimension: filterForm.thirdDimension,
                varType: filterForm.variableType,
                chName: filterForm.cnName,
                enName: filterForm.enName,
                pageNum: currentPage.value,
                pageSize: pageSize.value,
                roleId: obj.value.roleId
            }
            roleVarDictPage(params).then(res => {
                if (res.code == 200) {
                    tableData.value = res.rows;
                    total.value = res.total;
                    loading.value = false;
                    nextTick(() => {
                        console.log('tableData.value1111', tableData.value)
                        console.log('数据加载完成，当前全选状态:', isQuanXuanFlag.value)

                        // 检查用户的操作状态来决定选中逻辑
                        const hasCheckedField = tableData.value.some(row => row.hasOwnProperty('checked'))
                        const savedQuanXuanFlag = localStorage.getItem('isQuanXuanFlag')
                        const danqian_selectItems = JSON.parse(localStorage.getItem('danqian_selectItems')) || []

                        if (isQuanXuanFlag.value) {
                            // 用户点击了全选，使用全选逻辑
                            let invert_ids = JSON.parse(localStorage.getItem('invert_ids')) || []

                            tableData.value.forEach(row => {
                                // 全选状态下：默认选中，但要排除反选的项目
                                if (!invert_ids.includes(row.id)) {
                                    multipleTable.value.toggleRowSelection(row, true)
                                } else {
                                    multipleTable.value.toggleRowSelection(row, false)
                                }
                            })
                            console.log('全选状态下使用全选逻辑，invert_ids:', invert_ids)
                        } else if (savedQuanXuanFlag === 'false') {
                            // 用户主动取消了全选，完全清空状态，忽略后端checked字段
                            tableData.value.forEach(row => {
                                multipleTable.value.toggleRowSelection(row, false)
                            })
                            multipleSelection.value = []
                            console.log('用户取消全选后的状态，完全清空所有勾选，忽略后端checked字段')
                        } else {
                            // 默认状态（用户未操作过全选）或其他情况，优先使用后端checked字段
                            if (hasCheckedField) {
                                // 有后端checked字段，勾选所有checked==true的行
                                console.log('默认状态（未操作过全选），开始处理后端checked字段，数据:', tableData.value)
                                console.log('当前savedQuanXuanFlag状态:', savedQuanXuanFlag)

                                tableData.value.forEach(row => {
                                    if (row.checked === true) {
                                        multipleTable.value.toggleRowSelection(row, true)
                                        console.log('默认状态勾选行:', row.id, row.checked)
                                    } else {
                                        multipleTable.value.toggleRowSelection(row, false)
                                    }
                                })

                                // 更新multipleSelection以保持状态同步
                                multipleSelection.value = tableData.value.filter(row => row.checked === true).map(row => row.id)
                                console.log('默认状态，使用后端checked字段，已勾选的行ID:', multipleSelection.value)
                            } else {
                                // 没有后端checked字段，使用单独选择逻辑
                                let danqian_selectItems = JSON.parse(localStorage.getItem('danqian_selectItems')) || []
                                tableData.value.forEach(row => {
                                    if (danqian_selectItems.includes(row.id)) {
                                        multipleTable.value.toggleRowSelection(row, true)
                                    } else {
                                        multipleTable.value.toggleRowSelection(row, false)
                                    }
                                })
                                multipleSelection.value = danqian_selectItems
                                console.log('无checked字段，使用单独选择逻辑，danqian_selectItems:', danqian_selectItems)
                            }
                        }
                        // 数据加载完成，重置加载标志
                        isLoadingData.value = false
                        console.log('数据加载完成，最终全选状态:', isQuanXuanFlag.value)

                        // 强制恢复全选状态（防止被意外重置）
                        setTimeout(() => {
                            const savedFlag = localStorage.getItem('isQuanXuanFlag')
                            if (savedFlag !== null) {
                                const shouldBeSelected = JSON.parse(savedFlag)
                                if (isQuanXuanFlag.value !== shouldBeSelected) {
                                    console.log('强制恢复全选状态:', shouldBeSelected)
                                    isQuanXuanFlag.value = shouldBeSelected
                                }
                            }
                        }, 50) // 延迟50ms确保所有DOM更新完成
                    })
                }
            })
        } catch (error) {
            console.error('获取变量列表失败:', error);
            isLoadingData.value = false // 出错时也要重置标志
        } finally {
            // loading.value = false;
        }
    };

    // const selectEvent = val => {
    //     console.log('selectEvent:', val)
    //     //获取之前表格之前选择的items
    //     // const pre_selectItems = JSON.parse(localStorage.getItem("pre_selectItems")) || []

    //     // console.log("pre_selectItems", pre_selectItems)

    //     multipleSelection.value = val?.map(item => item.id)
    //     console.log(multipleSelection.value, multipleSelection.value)
    //     localStorage.setItem('danqian_selectItems', JSON.stringify([...new Set(multipleSelection.value)]))
    // }


    // 处理全选
    const handleSelectAll = (selection) => {
        // 如果正在加载数据，不处理选择变化
        if (isLoadingData.value) {
            return
        }

        if (isQuanXuanFlag.value) {
            // 全选状态下，更新 invert_ids
            const pageIds = tableData.value.map(row => row.id)
            let invert_ids = JSON.parse(localStorage.getItem('invert_ids')) || []
            // 取消选中的加入 invert_ids，选中的从 invert_ids 移除
            pageIds.forEach(id => {
                if (!selection.map(row => row.id).includes(id)) {
                    if (!invert_ids.includes(id)) invert_ids.push(id)
                } else {
                    invert_ids = invert_ids.filter(item => item !== id)
                }
            })
            localStorage.setItem('invert_ids', JSON.stringify(invert_ids))
        } else {
            // 非全选状态下，直接存储当前页选中的id
            const selectedIds = selection.map(row => row.id)
            let danqian_selectItems = JSON.parse(localStorage.getItem('danqian_selectItems')) || []
            // 合并去重
            danqian_selectItems = Array.from(new Set([...danqian_selectItems.filter(id => !tableData.value.map(row => row.id).includes(id)), ...selectedIds]))
            localStorage.setItem('danqian_selectItems', JSON.stringify(danqian_selectItems))
            multipleSelection.value = danqian_selectItems
        }
    }

    const handleSelect = (selection, row) => {
        // 如果正在加载数据，不处理选择变化
        if (isLoadingData.value) {
            return
        }

        if (isQuanXuanFlag.value) {
            let invert_ids = JSON.parse(localStorage.getItem('invert_ids')) || []
            if (selection) {
                // 选中，移除反选
                invert_ids = invert_ids.filter(id => id !== row.id)
            } else {
                // 取消选中，加入反选
                if (!invert_ids.includes(row.id)) invert_ids.push(row.id)
            }
            localStorage.setItem('invert_ids', JSON.stringify(invert_ids))
        } else {
            let danqian_selectItems = JSON.parse(localStorage.getItem('danqian_selectItems')) || []
            if (selection) {
                if (!danqian_selectItems.includes(row.id)) danqian_selectItems.push(row.id)
            } else {
                danqian_selectItems = danqian_selectItems.filter(id => id !== row.id)
            }
            localStorage.setItem('danqian_selectItems', JSON.stringify(danqian_selectItems))
            multipleSelection.value = danqian_selectItems
        }
    }


    const callback = row => {
        console.log('row: ', row)
        return row?.checked ? 1 : 0
    }
    const disabled1Handle = val => {
        if (!val) {
            filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined

        } else {
            filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined
            getsecondDimensionOptions({
                firstDimension: val
            }).then(res => {
                if (res.code == 200) {
                    secondDimensionOptions.value = res.data
                    console.log(res)
                }
            }).catch(err => {
                console.log('err', err)
            })
        }
    }

    const disabled2Handle = val => {
        if (!val) {
            filterForm.thirdDimension = undefined

        } else {
            // filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined
            getthirdDimensionOptions({
                secondDimension: val
            }).then(res => {
                if (res.code == 200) {
                    thirdDimensionOptions.value = res.data
                    console.log(res)
                }
            }).catch(err => {
                console.log('err', err)
            })

        }
    }


    onMounted(() => {

        localStorage.removeItem('pre_selectItems')
        localStorage.removeItem('danqian_selectItems')

        // 恢复全选状态
        const savedQuanXuanFlag = localStorage.getItem('isQuanXuanFlag')
        if (savedQuanXuanFlag !== null) {
            isQuanXuanFlag.value = JSON.parse(savedQuanXuanFlag)
            console.log('页面加载时恢复全选状态:', isQuanXuanFlag.value)
        }

        obj.value = JSON.parse(decodeURIComponent(route.query.row))
        // console.log(, '==query=')
        //一级维度选项
        getfirstDimensionOptions({
            dictType: 'biz_first_dimension'
        }).then(res => {
            if (res.code == 200) {
                firstDimensionOptions.value = res.data
                console.log(res)
            }
        }).catch(err => {
            console.log('err', err)
        })


        //变量类型选项
        getDataList({
            dictType: 'biz_var_type'
        }).then(res => {
            if (res.code == 200) {
                variableTypeOptions.value = res.data
                console.log(res, 'aaaaaaaaa')
            }
        }).catch(err => {
            console.log('err', err)
        })


        fetchVariables();
        // fetchRoleDataPermissions();
    });
    const getRowKeys = row => {
        return row.id
    }

    const Quanxuanhandler = val => {
        console.log('Quanxuanhandler', val, isQuanXuanFlag.value)
        // 保存全选状态到本地存储
        localStorage.setItem('isQuanXuanFlag', JSON.stringify(val))
        console.log('全选状态已保存到localStorage:', val)
    }

    // 监控全选状态变化
    watch(isQuanXuanFlag, (newVal, oldVal) => {
        console.log('全选状态发生变化:', oldVal, '->', newVal)
        console.trace('全选状态变化的调用栈:')
    })

    watch(isQuanXuanFlag, async (val) => {
        await nextTick();
        console.log('执行全选逻辑，当前状态:', val)
        if (val) {
            // 全选，清空反选id
            localStorage.setItem('invert_ids', JSON.stringify([]))
            // 选中当前页所有
            tableData.value.forEach(row => {
                multipleTable.value.toggleRowSelection(row, true);
            });
            // 清空单独选择
            localStorage.setItem('danqian_selectItems', JSON.stringify([]))
        } else {
            // 取消全选，清空所有选中状态
            localStorage.setItem('invert_ids', JSON.stringify([]))
            localStorage.setItem('danqian_selectItems', JSON.stringify([]))

            // 取消当前页所有选中状态
            tableData.value.forEach(row => {
                multipleTable.value.toggleRowSelection(row, false)
            })
            multipleSelection.value = []
            console.log('取消全选，清空所有勾选状态')
        }
    })

</script>

<template>
    <div class="data-permission-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <h1 class="page-title">数据权限配置 - {{ obj.roleName }}</h1>
                <div class="header-actions">
                    <el-button @click="router.push('/systemManagement/roleManagement')" type="info" plain>返回</el-button>
                    <el-button @click="saveDataPermissions" type="primary">保存</el-button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-grid">
                <div class="filter-item">
                    <label class="filter-label">一级维度</label>
                    <el-select @change="disabled1Handle" v-model="filterForm.firstDimension" class="filter-select"
                        clearable>
                        <el-option v-for="option in firstDimensionOptions" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />

                    </el-select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">二级维度</label>
                    <el-select :disabled="!filterForm.firstDimension" @change="disabled2Handle"
                        v-model="filterForm.secondDimension" class="filter-select" clearable>
                        <el-option v-for="option in secondDimensionOptions" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">三级维度</label>
                    <el-select :disabled="!filterForm.secondDimension || !filterForm.firstDimension"
                        v-model="filterForm.thirdDimension" class="filter-select" clearable>
                        <el-option v-for="option in thirdDimensionOptions" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">变量类型</label>
                    <el-select v-model="filterForm.variableType" class="filter-select" clearable>
                        <el-option v-for="option in variableTypeOptions" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </div>
            </div>
            <div class="filter-grid-secondary">
                <div class="filter-item">
                    <label class="filter-label">中文名</label>
                    <el-input v-model="filterForm.cnName" placeholder="请输入中文名" class="filter-input" />
                </div>
                <div class="filter-item">
                    <label class="filter-label">英文名</label>
                    <el-input v-model="filterForm.enName" placeholder="请输入英文名" class="filter-input" />
                </div>
            </div>
            <div class="filter-actions">
                <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
            </div>
            <div class="filter-actions" style="justify-content: flex-start;">
                <el-checkbox v-model="isQuanXuanFlag" label="全选" size="large" @change="Quanxuanhandler" />
            </div>
        </div>

        <!-- Table Section -->
        <div class="table-section">
            <el-table ref="multipleTable" v-loading="loading" :data="tableData" :row-key="getRowKeys"
                @selection-change="handleSelectAll" class="data-table" highlight-current-row @select="handleSelect"
                v-model:selection="multipleSelection" border stripe>
                <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
                <el-table-column prop="id" label="序号" width="80" align="center" />
                <el-table-column prop="varId" label="ID" width="120" align="center" />
                <el-table-column prop="firstDimension" label="一级维度" min-width="120" />
                <el-table-column prop="secondDimension" label="二级维度" min-width="120" />
                <el-table-column prop="thirdDimension" label="三级维度" min-width="120" />
                <el-table-column prop="varType" label="变量类型" width="100" align="center" />
                <el-table-column prop="chName" label="中文名" min-width="150" />
                <el-table-column prop="enName" label="英文名" min-width="150" />
            </el-table>

            <!-- 分页 -->
            <div class="mt-6 flex justify-end">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                    :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" class="custom-pagination" />
            </div>
        </div>
    </div>
</template>

<style scoped>
    .data-permission-container {
        padding: 24px;
        background-color: #f5f7fa;
        min-height: 100vh;
    }

    .header-section {
        margin-bottom: 24px;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        padding: 20px 24px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }

    .page-title {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
    }

    .filter-section {
        background-color: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .filter-grid-secondary {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 24px;
    }

    .filter-item {
        display: flex;
        /* flex-direction: column; */
        /* gap: 8px; */
    }

    .filter-label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        display: flex;
        align-items: center;
        width: 80px;
    }

    .filter-select,
    .filter-input {
        width: 50%;
    }

    /* .filter-input {
  width: 40%;
} */

    .filter-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;
    }

    .table-section {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .data-table {
        width: 100%;
    }

    .pagination-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 16px 24px;
        /* border-top: 1px solid #ebeef5; */
    }

    .custom-pagination {
        --el-pagination-button-bg-color: #fff;
        --el-pagination-hover-color: #409eff;
        display: flex;
        justify-content: flex-end;
        padding: 16px 0;
    }

    .pagination-info {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .pagination-text {
        font-size: 14px;
        color: #606266;
    }

    .pagination-select {
        width: 100px;
    }

    .pagination {
        margin: 0;
    }

    :deep(.el-table th) {
        background-color: #f5f7fa;
        font-weight: 600;
        color: #303133;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
        background-color: #fafafa;
    }

    :deep(.el-button) {
        font-weight: 500;
    }

    :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
    }

    :deep(.el-input__wrapper:hover) {
        box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    :deep(.el-input__wrapper.is-focus) {
        box-shadow: 0 0 0 1px #409eff inset;
    }
</style>