<template>
  <div class="app-container home">
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      
        欢迎来到企业信用决策风险特征平台
    

    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";

</script>



<style scoped lang="scss">
.home {
  min-height: calc(100vh - 134px);
  display: flex;
  justify-content: center;
  align-items: center;
  .main-content {
    // min-height: 835px;
    font-size: 24px;
    font-weight: bold;
    
  }
  .stats-grid {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
    .stat-card {
      flex: 1;
      .card-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .icon-wrapper {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          > svg {
            width: 60%;
            height: 60%;
          }
        }
        .stat-value {
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .task-card {
    margin-top: 20px;
  }
}
</style>

