<template>
  <div class="batch-progress-container">
    <div class="header-section">
      <h1 class="page-title">跑批进度</h1>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="任务名称">
              <el-input v-model="filters.taskName" placeholder="请输入任务名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务类型">
              <el-select v-model="filters.taskType" placeholder="请选择任务类型" class="w-full" clearable style="width: 100%;">
                <el-option label="全部" value="" />
                <el-option v-for="type in taskTypeOptions" :key="type" :label="type" :value="type" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行状态">
              <el-select v-model="filters.status" placeholder="请选择执行状态" class="w-full" clearable style="width: 100%;">
                <el-option label="全部" value="" />
                <el-option v-for="status in statusOptions" :key="status" :label="status" :value="status" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行开始日期">
              <el-date-picker v-model="filters.startDate" type="date" placeholder="选择开始日期" class="w-full" style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行结束日期">
              <el-date-picker v-model="filters.endDate" type="date" placeholder="选择结束日期" class="w-full" style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" style="display: flex; ">
              <el-button @click="resetFilters" plain>重置</el-button>
              <el-button type="primary" @click="applyFilters">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <template #header>
            <div class="stats-header">
              <span class="stats-title">总任务数</span>
            </div>
          </template>
          <div class="stats-value total">{{ stats.totalTasks }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <template #header>
            <div class="stats-header">
              <span class="stats-title">运行中</span>
            </div>
          </template>
          <div class="stats-value running">{{ stats.runningTasks }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <template #header>
            <div class="stats-header">
              <span class="stats-title">已完成</span>
            </div>
          </template>
          <div class="stats-value completed">{{ stats.completedTasks }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <template #header>
            <div class="stats-header">
              <span class="stats-title">失败</span>
            </div>
          </template>
          <div class="stats-value failed">{{ stats.failedTasks }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="paginatedTasks" 
        style="width: 100%" 
        v-loading="loading"
        border
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="name" label="任务名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="type" label="任务类型" width="120" align="center" />
        <el-table-column prop="startTime" label="开始时间" width="160" align="center" />
        <el-table-column prop="endTime" label="结束时间" width="160" align="center">
          <template #default="{ row }">
            {{ row.endTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="执行进度" width="200" align="center">
          <template #default="{ row }">
            <div class="progress-cell">
              <span class="progress-text">{{ row.progress }}%</span>
              <el-progress 
                :percentage="row.progress"
                :status="getProgressStatus(row.status)"
                :stroke-width="10"
                class="progress-bar"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="执行状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="light">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" link @click="viewTaskDetail(row)">查看</el-button>
              <el-button 
                v-if="row.status === '运行中'" 
                type="danger" 
                link 
                @click="cancelTask(row.id)"
              >取消</el-button>
              <el-button 
                v-if="row.status === '失败' || row.status === '已取消'" 
                type="success" 
                link 
                @click="retryTask(row.id)"
              >重试</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[8, 16, 24, 32]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 模拟数据 - 统计信息
const stats = reactive({
  totalTasks: 45,
  runningTasks: 6,
  completedTasks: 32,
  failedTasks: 7
});

// 模拟数据 - 任务记录
const taskRecords = ref([
  { id: 1, name: '客户数据更新任务', type: '数据同步', startTime: '2024-05-20 00:30:00', endTime: '2024-05-20 01:15:00', progress: 100, status: '已完成' },
  { id: 2, name: '企业信用评分计算', type: '批量计算', startTime: '2024-05-20 01:00:00', endTime: null, progress: 67, status: '运行中' },
  { id: 3, name: '风险模型训练', type: '模型训练', startTime: '2024-05-19 23:00:00', endTime: '2024-05-20 02:30:00', progress: 100, status: '已完成' },
  { id: 4, name: '月度报表生成', type: '报表生成', startTime: '2024-05-19 22:00:00', endTime: '2024-05-19 23:45:00', progress: 100, status: '已完成' },
  { id: 5, name: '数据质量检查', type: '数据验证', startTime: '2024-05-19 21:30:00', endTime: '2024-05-19 22:10:00', progress: 100, status: '已完成' },
  { id: 6, name: '交易数据备份', type: '数据备份', startTime: '2024-05-19 20:00:00', endTime: null, progress: 35, status: '运行中' },
  { id: 7, name: '欺诈检测模型评估', type: '模型评估', startTime: '2024-05-19 19:00:00', endTime: '2024-05-19 19:25:00', progress: 100, status: '已完成' },
  { id: 8, name: '数据资产清单更新', type: '数据同步', startTime: '2024-05-19 18:00:00', endTime: '2024-05-19 18:30:00', progress: 100, status: '已完成' },
  { id: 9, name: '客户分群分析', type: '批量计算', startTime: '2024-05-19 17:00:00', endTime: '2024-05-19 17:40:00', progress: 80, status: '失败' },
  { id: 10, name: '营销模型训练', type: '模型训练', startTime: '2024-05-19 16:00:00', endTime: null, progress: 45, status: '运行中' },
  { id: 11, name: '系统性能指标统计', type: '报表生成', startTime: '2024-05-19 15:00:00', endTime: '2024-05-19 15:20:00', progress: 100, status: '已完成' },
  { id: 12, name: '历史数据清理', type: '数据清理', startTime: '2024-05-19 14:00:00', endTime: '2024-05-19 14:10:00', progress: 30, status: '已取消' },
  { id: 13, name: '用户行为分析', type: '批量计算', startTime: '2024-05-19 13:00:00', endTime: '2024-05-19 13:45:00', progress: 100, status: '已完成' },
  { id: 14, name: '风险预警规则更新', type: '规则更新', startTime: '2024-05-19 12:00:00', endTime: '2024-05-19 12:15:00', progress: 100, status: '已完成' },
  { id: 15, name: '数据库索引优化', type: '系统维护', startTime: '2024-05-19 11:00:00', endTime: null, progress: 90, status: '失败' },
]);

// 筛选条件
const filters = reactive({
  taskName: '',
  taskType: '',
  status: '',
  startDate: '',
  endDate: ''
});

// 选项数据
const taskTypeOptions = ['数据同步', '批量计算', '模型训练', '报表生成', '数据验证', '数据备份', '模型评估', '数据清理', '规则更新', '系统维护'];
const statusOptions = ['运行中', '已完成', '失败', '等待中', '已取消'];

// 加载状态
const loading = ref(false);

// 获取进度条状态
const getProgressStatus = (status) => {
  switch (status) {
    case '运行中': return '';
    case '已完成': return 'success';
    case '失败': return 'exception';
    case '等待中': return 'warning';
    case '已取消': return 'info';
    default: return '';
  }
};

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case '运行中': return 'primary';
    case '已完成': return 'success';
    case '失败': return 'danger';
    case '等待中': return 'warning';
    case '已取消': return 'info';
    default: return '';
  }
};

// 应用筛选
const applyFilters = () => {
  currentPage.value = 1;
};

// 重置筛选
const resetFilters = () => {
  filters.taskName = '';
  filters.taskType = '';
  filters.status = '';
  filters.startDate = '';
  filters.endDate = '';
  currentPage.value = 1;
};

// 查看任务详情
const viewTaskDetail = (task) => {
  ElMessageBox.alert(
    `任务详情:\n类型: ${task.type}\n开始时间: ${task.startTime}\n结束时间: ${task.endTime || '未结束'}\n进度: ${task.progress}%\n状态: ${task.status}`,
    task.name,
    {
      confirmButtonText: '确定',
    }
  );
};

// 取消任务
const cancelTask = (taskId) => {
  ElMessageBox.confirm(
    `确定要取消ID为 "${taskId}" 的任务吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const task = taskRecords.value.find(t => t.id === taskId);
    if (task) {
      task.status = '已取消';
      ElMessage.success('任务已取消');
    }
  }).catch(() => {});
};

// 重试任务
const retryTask = (taskId) => {
  ElMessageBox.confirm(
    `确定要重试ID为 "${taskId}" 的任务吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const task = taskRecords.value.find(t => t.id === taskId);
    if (task) {
      task.status = '等待中';
      task.progress = 0;
      task.startTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
      task.endTime = null;
      ElMessage.success('任务已重新开始');
    }
  }).catch(() => {});
};

// 过滤任务记录
const filteredTasks = computed(() => {
  return taskRecords.value.filter(task => {
    const nameMatch = !filters.taskName || task.name.toLowerCase().includes(filters.taskName.toLowerCase());
    const typeMatch = !filters.taskType || task.type === filters.taskType;
    const statusMatch = !filters.status || task.status === filters.status;
    
    let startDateMatch = true;
    let endDateMatch = true;
    
    if (filters.startDate) {
      const taskStartDate = new Date(task.startTime);
      const filterStartDate = new Date(filters.startDate);
      startDateMatch = taskStartDate >= filterStartDate;
    }
    
    if (filters.endDate) {
      const taskStartDate = new Date(task.startTime);
      const filterEndDate = new Date(filters.endDate);
      filterEndDate.setHours(23, 59, 59, 999);
      endDateMatch = taskStartDate <= filterEndDate;
    }
    
    return nameMatch && typeMatch && statusMatch && startDateMatch && endDateMatch;
  }).sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
});

// 分页
const currentPage = ref(1);
const pageSize = ref(8);
const totalItems = computed(() => filteredTasks.value.length);

const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTasks.value.slice(start, end);
});

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 获取任务数据
const fetchTaskData = async () => {
  loading.value = true;
  try {
    console.log('获取跑批进度数据...');
    // 在实际应用中应调用API
    // taskRecords.value = await api.getTaskProgress();
    // stats.totalTasks = await api.getTaskStats().totalTasks;
    // 等等...
  } catch (error) {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchTaskData();
});
</script>

<style scoped>
.batch-progress-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-section {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.filter-form {
  padding: 8px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 8px;
  transition: transform 0.3s;
}

.stats-card:hover {
  transform: translateY(-4px);
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-title {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  text-align: center;
  padding: 16px 0;
}

.stats-value.total { color: #3b82f6; }
.stats-value.running { color: #f59e0b; }
.stats-value.completed { color: #10b981; }
.stats-value.failed { color: #ef4444; }

.table-card {
  border-radius: 8px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  min-width: 48px;
  text-align: right;
}

.progress-bar {
  flex: 1;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f9fafb;
}

:deep(.el-table th) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-table td) {
  color: #4b5563;
}

:deep(.el-pagination) {
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-hover-color: #3b82f6;
}
</style> 