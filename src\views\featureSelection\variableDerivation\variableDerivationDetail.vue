<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
// import { procedureApi } from '../utils/api'
import { ElCard, ElSkeleton, ElTable, ElTableColumn, ElTag } from 'element-plus'

const route = useRoute()
const procedureId = route.params.id

// 加载状态
const loading = ref(true)

// 存储过程详情
const procedureDetail = ref({
  id: 0,
  name: '',
  description: '',
  version: '',
  tables: [],
  updateDate: '',
  maintainAccount: '',
  code: '',
  dependencies: []
})

// 获取存储过程详情
const fetchProcedureDetail = async () => {
  loading.value = true
  try {
    // const response = await procedureApi.getProcedureById(procedureId)
    procedureDetail.value ={
    "id": "1",
    "name": "数据清洗存储过程",
    "tables": [
        "raw_data",
        "clean_data"
    ],
    "description": "清洗原始数据，处理缺失值和异常值",
    "version": "1.0.3",
    "updateDate": "2024-05-15",
    "maintainAccount": "admin",
    "code": "CREATE PROCEDURE sp_data_cleaning\nAS\nBEGIN\n    -- 清理数据逻辑\n    DELETE FROM raw_data WHERE value IS NULL;\n    \n    -- 插入到清洗后的表\n    INSERT INTO clean_data\n    SELECT * FROM raw_data\n    WHERE quality_score > 0.7;\nEND;",
    "dependencies": [
        {
            "name": "raw_data",
            "type": "表",
            "version": "1.0"
        },
        {
            "name": "clean_data",
            "type": "表",
            "version": "1.0"
        }
    ]
}
    loading.value = false
  } catch (error) {
    console.error('获取存储过程详情失败:', error)
    loading.value = false
  }
}

// 在页面加载时获取存储过程详情
onMounted(() => {
  fetchProcedureDetail()
})
</script>

<template>
  <div class="procedure-detail">
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div class="skeleton-content">
          <el-skeleton-item variant="h3" style="width: 50%" />
          <el-skeleton-item variant="text" style="width: 100%" />
          <el-skeleton-item variant="text" style="width: 80%" />
        </div>
      </template>
      <template #default>
        <!-- 存储过程基本信息 -->
        <el-card class="mb-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 class="text-xl font-semibold mb-4">{{ procedureDetail.name }}</h2>
              <p class="text-gray-600 mb-4">{{ procedureDetail.description }}</p>
              <div class="info-item">
                <span class="label">版本号:</span>
                <el-tag size="small" class="ml-2">{{ procedureDetail.version }}</el-tag>
              </div>
              <div class="info-item">
                <span class="label">更新日期:</span>
                <span class="ml-2">{{ procedureDetail.updateDate }}</span>
              </div>
              <div class="info-item">
                <span class="label">维护账号:</span>
                <span class="ml-2">{{ procedureDetail.maintainAccount }}</span>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium mb-3">关联表</h3>
              <el-card shadow="never" class="bg-gray-50">
                <ul class="space-y-2">
                  <li v-for="(table, index) in procedureDetail.tables" :key="index" class="text-sm">
                    • {{ table }}
                  </li>
                  <li v-if="procedureDetail.tables.length === 0" class="text-sm text-gray-500">无关联表</li>
                </ul>
              </el-card>
            </div>
          </div>
        </el-card>

        <!-- 代码展示 -->
        <el-card class="mb-4">
          <template #header>
            <div class="card-header">
              <span>存储过程代码</span>
            </div>
          </template>
          <div class="code-block">
            <pre><code>{{ procedureDetail.code }}</code></pre>
          </div>
        </el-card>

        <!-- 依赖项 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span>依赖项</span>
            </div>
          </template>
          <el-table :data="procedureDetail.dependencies" style="width: 100%">
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="version" label="版本" />
            <template #empty>
              <div class="text-center text-gray-500 py-4">无依赖项</div>
            </template>
          </el-table>
        </el-card>
      </template>
    </el-skeleton>
  </div>
</template>

<style scoped>
.procedure-detail {
  padding: 20px;
}

.skeleton-content {
  padding: 20px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #606266;
}

.code-block {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 16px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  color: #fff;
  font-family: monospace;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}
</style> 