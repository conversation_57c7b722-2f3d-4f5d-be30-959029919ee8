import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login',
// '/largeScreen',
  // '/index', '/register', 
  // '/DataVisualization/dataBigScreen'
  // ,'/systemManagement/accountManagement',
  // '/systemManagement/roleManagement',
  // '/systemManagement/sandboxAccountManagement',
  // '/systemManagement/sandboxAccount',
  // "/featureSelection/variableDictionary",
  // '/featureSelection/VariableDetail',
  // "/featureSelection/variableDerivation",
  // "/featureSelection/sampleMatching",
  // '/featureSelection/variableDerivationDetail'
];

router.beforeEach((to, from, next) => {
  console.log('getToken', to?.fullPath != '/featureSelection/variableDictionary' , to, to.path)

  NProgress.start()
  // if(to?.path != '/featureSelection/variableDictionary' && to?.path != '/featureSelection/VariableDetail'){
  //   localStorage.removeItem('params')
  // }
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      console.log('getToken')
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      console.log('whiteList')
      next()
    } else {
      console.log('useUserStore')
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true
        console.log('useUserStore')
        // 判断当前用户是否已拉取完user_info信息
        useUserStore().getInfo().then(() => {
          isRelogin.show = false
          usePermissionStore().generateRoutes().then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            accessRoutes.forEach(route => {
              if (!isHttp(route.path)) {
                router.addRoute(route) // 动态添加可访问路由表
              }
            })
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          console.log('logOut--err', err)
          useUserStore().logOut().then(() => {
            ElMessage.error(err)
            next({ path: '/' })
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
