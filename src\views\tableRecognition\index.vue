<template>
  <div class="app-container">
    <!-- 头部区域 -->
    <div class="main-header">
      <div class="header-content">
        <div class="left-section">
          <h2 class="title">大模型表格识别</h2>
        </div>
        <el-button type="primary" :icon="Refresh" @click="refreshList"
          >刷新列表</el-button
        >
      </div>
    </div>

    <div class="main-content">
      <div class="content-wrapper">
        <!-- 左侧图片列表 -->
        <div class="file-panel">
          <el-card class="search-filter">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索图片"
              :prefix-icon="Search"
              clearable
            />
            <div class="filter-tabs">
              <el-radio-group v-model="filterStatus" size="small">
                <el-radio-button label="全部" />
                <el-radio-button label="待处理" />
                <el-radio-button label="已完成" />
              </el-radio-group>
            </div>
          </el-card>
          <el-card style="height: 540px;overflow-y: scroll;">
            <template #header>
              <div class="card-header">待处理图片 ({{ filteredImages.length }})</div>
            </template>
            <el-scrollbar class="image-list">
              <div
                v-for="(image, index) in paginatedImages"
                :key="image.id"
                class="image-item"
                :class="{ selected: selectedImage === getOriginalIndex(index) }"
                @click="selectImage(getOriginalIndex(index))"
              >
                <div class="thumbnail">
                  <img :src="image.thumbnail" />
                </div>
                <div class="image-info">
                  <div class="name">{{ image.name }}</div>
                  <div class="folder">
                    <i class="ri-folder-line"></i>
                    {{ image.folder }}
                  </div>
                  <el-tag :type="statusType(image.status)" size="small">
                    {{ image.status }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
			<div class="pagination-wrapper">
            <el-pagination
			 class="mt-4 text-right"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-size="pageSize"
              layout="total, prev, pager, next, jumper"
              :total="filteredImages.length"
            />
			</div>
          </el-card>
        </div>

        <!-- 右侧工作区 -->
        <div class="workspace">
          <!-- 操作工具栏 -->
          <el-card class="toolbar">
            <div class="toolbar-container">
              <div class="current-file">
                当前处理：<span class="highlight blue-text">{{ currentImage.name }}</span>
              </div>
              <div class="actions">
                <el-button type="success" :icon="Check">数据入库</el-button>
                <el-button type="warning" :icon="User">转入人工核对</el-button>
              </div>
            </div>
          </el-card>

          <!-- 图片和识别结果 -->
          <div class="grid-container">
            <!-- 原图预览 -->
            <el-card class="image-preview">
              <template #header>
                <div class="card-header">原图</div>
              </template>
              <el-scrollbar>
                <img 
                  :src="currentImage.full" 
                  class="preview-image" 
                  @click="showImagePreview(currentImage.full)"
                  style="cursor: pointer;"
                />
              </el-scrollbar>
            </el-card>

            <!-- 识别结果 -->
            <el-card class="result-preview">
              <template #header>
                <div class="card-header">
                  <span>识别结果</span>
                  <div class="status-info">
                    <el-tag :type="statusType(currentImage.status)">
                      {{ currentImage.status }}
                    </el-tag>
                    <div class="accuracy">
                      精确度：<span class="value"
                        >{{ currentImage.accuracy || "-" }}%</span
                      >
                    </div>
                  </div>
                </div>
              </template>

              <el-alert
                v-if="currentImage.status === '已识别'"
                type="success"
                :closable="false"
                class="result-alert"
              >
                <template #default>
                  <span>识别完成！校验结果：</span>
                  <span class="verify-result">通过</span><br />
                  <span>
                    大模型已成功识别并转化为表格数据，可点击"数据入库"或"转入人工核对"进行后续处理。
                  </span>
                </template>
              </el-alert>

              <el-table :data="tableData" border class="data-table">
                <el-table-column prop="subject" label="科目" />
                <el-table-column prop="current" label="期末余额" />
                <el-table-column prop="initial" label="年初余额" />
              </el-table>
            </el-card>
          </div>

          <!-- 底部导航 -->
          <div class="footer-navigation">
            <el-button :icon="ArrowLeft" @click="prevImage">上一个</el-button>
            <el-button @click="nextImage"
              >下一个 <el-icon class="el-icon--right"><ArrowRight /></el-icon
            ></el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      :title="previewImageTitle"
      width="80%"
      :before-close="handleImagePreviewClose"
    >
      <img :src="previewImageUrl" style="width: 100%;" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  Refresh,
  Check,
  User,
  ArrowLeft,
  ArrowRight,
  Search,
} from "@element-plus/icons-vue";

// 图片数据
const images = ref([
  {
    id: 1,
    name: "IMG_89736512",
    folder: "深圳市华美兴泰科技股份有限公司21",
    status: "已识别",
    thumbnail: new URL(
      "@/assets/images/HuameiXingtai_2101.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/HuameiXingtai_2101.png", import.meta.url)
      .href,
    accuracy: 98.2,
    tableData: [
      {id:1, subject: "流动资产：", current: "", initial: "" },
      {
		id:2,
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  {
    id: 2,
    name: "IMG_89736513",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "待识别",
    thumbnail: new URL(
      "@/assets/images/gongsi.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/gongsi.png", import.meta.url)
      .href,
    accuracy: null,
    tableData: [
      { subject: "流动资产：", current: "", initial: "" },
      {
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  {
    id: 3,
    name: "IMG_89736513",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "待识别",
    thumbnail: new URL(
      "@/assets/images/gongsi.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/gongsi.png", import.meta.url)
      .href,
    accuracy: null,
    tableData: [
      { subject: "流动资产：", current: "", initial: "" },
      {
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  {
    id: 4,
    name: "IMG_89736513",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "待识别",
    thumbnail: new URL(
      "@/assets/images/gongsi.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/gongsi.png", import.meta.url)
      .href,
    accuracy: null,
    tableData: [
      { subject: "流动资产：", current: "", initial: "" },
      {
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  {
    id: 5,
    name: "IMG_89736513",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "待识别",
    thumbnail: new URL(
      "@/assets/images/gongsi.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/gongsi.png", import.meta.url)
      .href,
    accuracy: null,
    tableData: [
      { subject: "流动资产：", current: "", initial: "" },
      {
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  {
    id: 6,
    name: "IMG_89736513",
    folder: "深圳市华美兴泰科技股份有限公司22",
    status: "待识别",
    thumbnail: new URL(
      "@/assets/images/gongsi.png",
      import.meta.url
    ).href,
    full: new URL("@/assets/images/gongsi.png", import.meta.url)
      .href,
    accuracy: null,
    tableData: [
      { subject: "流动资产：", current: "", initial: "" },
      {
        subject: "货币资金",
        current: "45,678,921.00",
        initial: "39,982,145.00",
      },
    ],
  },
  // 其他图片数据...
]);

// 表格数据
const tableData = computed(
  () => images.value[selectedImage.value]?.tableData || []
);

// 状态管理
const searchKeyword = ref("");
const filterStatus = ref("全部");
const selectedImage = ref(0);

// 计算属性
const currentImage = computed(() => images.value[selectedImage.value] || {});

const filteredImages = computed(() => {
  return images.value.filter((image) => {
    const matchSearch =
      image.name.includes(searchKeyword.value) ||
      image.folder.includes(searchKeyword.value);
    const matchStatus =
      filterStatus.value === "全部" || image.status === filterStatus.value;
    return matchSearch && matchStatus;
  });
});

// 方法
const statusType = (status) => {
  return status === "待识别" ? "warning" : "success";
};

const selectImage = (index) => {
  selectedImage.value = index;
};

const refreshList = () => {
  console.log("刷新图片列表");
};

const prevImage = () => {
  if (selectedImage.value > 0) selectedImage.value--;
};

const nextImage = () => {
  if (selectedImage.value < images.value.length - 1) selectedImage.value++;
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const paginatedImages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredImages.value.slice(start, end);
});

const getOriginalIndex = (paginatedIndex) => {
  const start = (currentPage.value - 1) * pageSize.value;
  return filteredImages.value.indexOf(paginatedImages.value[paginatedIndex]);
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 图片预览相关
const imagePreviewVisible = ref(false);
const previewImageUrl = ref("");
const previewImageTitle = ref("原图预览");

const showImagePreview = (url) => {
  previewImageUrl.value = url;
  imagePreviewVisible.value = true;
};

const handleImagePreviewClose = () => {
  imagePreviewVisible.value = false;
};
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  width: 275px;
}
::v-deep .el-pagination__editor.el-input {
    width: 31px;
    height: 31px;
}
.content-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  .search-filter {
    margin-bottom: 24px;
  }
  .file-panel {
    width: 300px;
    min-width: 300px;
    margin-right: 24px;
    .filter-tabs {
      margin-top: 16px;
    }
  }
  .workspace {
    flex: 1;
    .toolbar-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .actions {
      margin-left: 24px;
    }
  }
  .result-alert {
    margin-bottom: 16px;
  }
  .grid-container {
    margin-top: 24px;
    display: flex;
    gap: 24px;
    .image-preview,
    .result-preview {
      flex: 1;
    }
    .preview-image {
      width: 100%;
      height: 100%;
    }
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .status-info {
        display: flex;
        align-items: center;
      }
      .accuracy {
        margin-left: 16px;
        font-size: 12px;
        .value {
          color: rgb(22 163 74 / var(--tw-text-opacity, 1));
        }
      }
    }
    .editor-content {
      width: 100%;
      .title-input {
        margin-bottom: 16px;
      }
    }
  }
  .footer-navigation {
    margin-top: 16px;
    text-align: right;
  }
  .image-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #e8e4e4;
    .thumbnail {
      display: inline-block;
      width: 64px;
      height: 64px;
      margin-right: 16px;
      > img {
        width: 100%;
        height: 100%;
      }
    }
    .image-info {
      flex: 1;
      .folder {
        font-size: 12px;
        color: #7f8083;
        margin: 6px 0;
      }
    }
  }
  .problem-panel {
    margin-top: 16px;
  }
}
.blue-text {
  color: blue;
}
</style>