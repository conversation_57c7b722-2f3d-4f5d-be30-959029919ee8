<template>
  <div class="model-list-container">
    <h1 class="page-title">模型列表</h1>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" label-width="120px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="ID">
              <el-input v-model="filters.id" placeholder="请输入ID" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="存储过程名称">
              <el-input v-model="filters.procedureName" placeholder="请输入存储过程名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="模型表名">
              <el-input v-model="filters.modelTableName" placeholder="请输入模型表名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="模型状态">
              <el-select v-model="filters.status" placeholder="请选择状态" class="w-full" clearable>
                <el-option label="全部" value="" />
                <el-option label="已部署" value="已部署" />
                <el-option label="未部署" value="未部署" />
                <el-option label="已停用" value="已停用" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="filter-buttons">
          <el-button @click="resetFilters" plain>重置</el-button>
          <el-button type="primary" @click="applyFilters">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="paginatedModels" 
        style="width: 100%" 
        v-loading="loading"
        border
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="id" label="ID" min-width="120" show-overflow-tooltip />
        <el-table-column prop="procedureName" label="存储过程名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="modelTableName" label="模型表名" min-width="180" show-overflow-tooltip />
        <el-table-column label="模型变量名" min-width="200">
          <template #default="{ row }">
            <el-tag 
              v-for="(variable, index) in row.modelVariables" 
              :key="index"
              class="variable-tag"
              size="small"
            >
              {{ variable }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deploymentForm" label="部署形式" min-width="120" align="center" />
        <el-table-column prop="status" label="模型状态" min-width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="dark">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewModel(row)">查看</el-button>
            <el-button 
              v-if="row.status !== '已停用'" 
              type="danger" 
              link 
              @click="toggleModelStatus(row.id, '已停用')"
            >
              停用
            </el-button>
            <el-button 
              v-if="row.status === '已停用'" 
              type="success" 
              link 
              @click="toggleModelStatus(row.id, '未部署')"
            >
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 模拟数据源
const allModelEntries = ref([
  { id: 'MODEL_DEPLOY_001', procedureName: 'SP_CREDIT_SCORE_V3', modelTableName: 'TBL_MODEL_CREDIT_V3_OUTPUT', modelVariables: ['age', 'income', 'loan_history', 'score'], deploymentForm: 'API接口', status: '已部署' },
  { id: 'MODEL_DEPLOY_002', procedureName: 'SP_FRAUD_DETECT_V1.2', modelTableName: 'TBL_MODEL_FRAUD_V1_OUTPUT', modelVariables: ['trans_amount', 'freq', 'location_risk', 'is_fraud'], deploymentForm: '批量任务', status: '已部署' },
  { id: 'MODEL_DEPLOY_003', procedureName: 'SP_RISK_EVAL_V2', modelTableName: 'TBL_MODEL_RISK_V2_OUTPUT', modelVariables: ['asset_value', 'debt_ratio', 'market_volatility', 'risk_level'], deploymentForm: 'API接口', status: '未部署' },
  { id: 'MODEL_DEPLOY_004', procedureName: 'SP_CHURN_PREDICT_V1', modelTableName: 'TBL_MODEL_CHURN_V1_OUTPUT', modelVariables: ['usage_months', 'service_calls', 'satisfaction_score', 'will_churn'], deploymentForm: 'API接口', status: '已停用' },
  { id: 'MODEL_DEPLOY_005', procedureName: 'SP_SALES_FORECAST_V2.1', modelTableName: 'TBL_MODEL_SALES_V2_OUTPUT', modelVariables: ['past_sales', 'promo_spend', 'season_factor', 'forecast_sales'], deploymentForm: '批量任务', status: '已部署' },
]);

const loading = ref(false);
const filters = reactive({
  id: '',
  procedureName: '',
  modelTableName: '',
  status: ''
});

const applyFilters = () => {
  currentPage.value = 1;
};

const resetFilters = () => {
  filters.id = '';
  filters.procedureName = '';
  filters.modelTableName = '';
  filters.status = '';
  currentPage.value = 1;
};

const filteredModels = computed(() => {
  return allModelEntries.value.filter(model => {
    const idMatch = filters.id ? model.id.toLowerCase().includes(filters.id.toLowerCase()) : true;
    const procNameMatch = filters.procedureName ? model.procedureName.toLowerCase().includes(filters.procedureName.toLowerCase()) : true;
    const tableNameMatch = filters.modelTableName ? model.modelTableName.toLowerCase().includes(filters.modelTableName.toLowerCase()) : true;
    const statusMatch = filters.status ? model.status === filters.status : true;
    return idMatch && procNameMatch && tableNameMatch && statusMatch;
  });
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => filteredModels.value.length);

const paginatedModels = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredModels.value.slice(start, end);
});

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 状态标签类型
const getStatusType = (status) => {
  const types = {
    '已部署': 'success',
    '未部署': 'warning',
    '已停用': 'danger'
  };
  return types[status] || 'info';
};

// 操作
const viewModel = (model) => {
  ElMessage({
    message: `查看模型详情: ${model.id}\n存储过程: ${model.procedureName}\n状态: ${model.status}`,
    type: 'info'
  });
};

const toggleModelStatus = (modelId, newStatus) => {
  const model = allModelEntries.value.find(m => m.id === modelId);
  if (model) {
    ElMessageBox.confirm(
      `确定要将模型 "${modelId}" 的状态更改为 "${newStatus}" 吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      model.status = newStatus;
      ElMessage({
        type: 'success',
        message: `模型 ${modelId} 状态已更新为 ${newStatus}`
      });
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消操作'
      });
    });
  }
};

// 模拟API请求
const fetchData = async () => {
  loading.value = true;
  try {
    console.log('获取模型列表数据...');
    // 实际应用中会调用API
    // allModelEntries.value = await api.getModels();
  } catch (error) {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.model-list-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.filter-form {
  padding: 8px 0;
}

.filter-buttons {
  margin-top: 8px;
  text-align: right;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.variable-tag {
  margin: 2px;
}

.pagination-container {
  margin-top: 20px;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
}
</style> 