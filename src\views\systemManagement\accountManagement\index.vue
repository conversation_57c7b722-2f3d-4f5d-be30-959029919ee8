<template>
    <div class="account-management p-6 bg-gray-50 min-h-screen memory-alert">
        <h1 class="text-2xl font-bold mb-8 text-gray-800">账号管理</h1>
        <div class="max-w-7xl mx-auto">


            <!-- 操作按钮和搜索 -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
                style="display: flex; justify-content: space-between; margin-bottom: 24px;">
                <div>
                    <el-button type="primary" @click="addAccountFn" class="!flex items-center" v-hasPermi="['system:user:add']">
                        <el-icon class="mr-2">
                            <Plus />
                        </el-icon>
                        新建账号
                    </el-button>
                </div>
                <div class="w-full sm:w-64" style="width: 200px;">
                    <el-input v-model="searchQuery" placeholder="搜索账号..." clearable class="!rounded-lg"
                        @keyup.enter="applyFilters">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </div>
            </div>

            <!-- 筛选区域 -->
            <el-card class="mb-6 !rounded-lg shadow-sm">
                <el-form :model="filters" label-width="80px" class="filter-form">
                    <el-row :gutter="20">
                        <!-- <el-col :xs="24" :sm="6">
                            <el-form-item label="部门">
                                <el-select v-model="filters.department" placeholder="全部" clearable class="w-full"
                                    style="width: 100%;">
                                    <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name"
                                        :value="dept.id" />
                                </el-select>
                            </el-form-item>
                        </el-col> -->
                        <el-col :xs="24" :sm="6">
                            <el-form-item label="角色">
                                <el-select v-model="filters.role" placeholder="全部" clearable class="w-full"
                                    style="width: 100%;">
                                    <el-option v-for="role in roleOptions" :key="role.roleId" :label="role.roleName"
                                        :value="role.roleId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="6">
                            <el-form-item label="状态">
                                <el-select v-model="filters.status" placeholder="全部" clearable class="w-full"
                                    style="width: 100%;">
                                    <el-option v-for="status in statusOptions" :key="status.dictLabel"
                                        :label="status.dictLabel" :value="status.dictValue" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :span="6">
                            <el-form-item class="!mb-0" style="text-align: left;">
                                <div class="flex gap-3" style="display: flex; justify-content: flex-end; width: 100%;">
                                    <div style="margin: 0 10px;">
                                        <el-button @click="resetFilters" class="!rounded-lg" v-hasPermi="['system:user:page']">重置</el-button>
                                    </div>
                                    <div>
                                        <el-button type="primary" @click="applyFilters"
                                            class="!rounded-lg" v-hasPermi="['system:user:page']">查询</el-button>
                                    </div>

                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-form>
            </el-card>

            <!-- 数据表格 -->
            <el-card class="!rounded-lg shadow-sm" style="margin-top: 24px;">
                <el-table :data="paginatedUsers" style="width: 100%" v-loading="dataLoading" class="custom-table"
                    :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#606266',
                        fontWeight: '600'
                    }">
                    <el-table-column type="index" label="序号" width="80" align="center" />
                    <el-table-column prop="userName" label="账号" min-width="120" />
                    <el-table-column prop="validityDate" label="账号有效期" min-width="120" />
                    <el-table-column prop="nickName" label="姓名" min-width="100" />
                    <!-- <el-table-column prop="deptName" label="部门" min-width="120" /> -->
                    <el-table-column prop="roleName" label="角色" min-width="120" />
                    <el-table-column prop="email" label="邮箱" min-width="180" />
                    <el-table-column prop="loginDate" label="最近登录" min-width="160" />
                    <el-table-column prop="status" label="状态" min-width="100">
                        <template #default="{ row }">
                            <el-tag
                                :type="row.statusStr === '启用' ? 'success' : row.statusStr === '禁用' ? 'danger' : 'warning'"
                                class="!rounded-full">
                                {{ row.statusStr }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="280" fixed="right">
                        <template #default="{ row }">
                            <div class="flex flex-wrap gap-2 operation">
                                <el-button link type="primary" @click="editUser(row)" v-hasPermi="['system:user:edit']">编辑</el-button>
                                <el-button v-if="row.status == '0'" link type="danger" @click="disableUser(row.userId)"
                                v-hasPermi="['system:user:status']"
                                >
                                    禁用
                                </el-button>
                                <el-button v-if="row.status == '1'" link type="success" @click="enableUser(row.userId)"
                                v-hasPermi="['system:user:status']"
                                >
                                    启用
                                </el-button>
                                <el-button link type="primary" @click="resetPassword(row.userId)"
                                  v-hasPermi="['system:user:pwd']"
                                >重置密码</el-button>
                                <el-button link type="primary" @click="manageSandboxAccounts(row)"
                                v-hasPermi="['system:user:sandboxUserPage']"
                                >沙箱账号</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="mt-6 flex justify-end">
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="totalItems"
                        :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        class="custom-pagination" />
                </div>
            </el-card>

            <!-- 新建用户对话框 -->
            <template v-if="showNewUserDialog">
                <el-dialog v-model="showNewUserDialog" title="新建账号" width="500px" class="custom-dialog" destroy-on-close
                    @close="cancleAddFn">
                    <el-form :model="newUser" label-width="100px" :rules="rules" ref="newUserForm" class="custom-form">
                        <el-form-item label="用户名" prop="username">
                            <el-input v-model="newUser.username" class="!rounded-lg" />
                        </el-form-item>
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="newUser.name" class="!rounded-lg" autocomplete="off" />
                        </el-form-item>
                        <el-form-item label="密码" prop="password">
                            <el-input v-model="newUser.password" type="password" class="!rounded-lg"
                                autocomplete="new-password" />
                        </el-form-item>
                        
                        <el-form-item label="角色" prop="role">
                            <el-select v-model="newUser.role" class="w-full !rounded-lg" style="width: 100%">
                                <el-option v-for="role in roleOptions" :key="role.roleId" :label="role.roleName"
                                    :value="role.roleId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="newUser.email" class="!rounded-lg" />
                        </el-form-item>
                        <el-form-item label="状态" prop="status">
                            <el-select v-model="newUser.status" class="w-full !rounded-lg" style="width: 100%">
                                <el-option v-for="status in statusOptions" :key="status.dictLabel"
                                    :label="status.dictLabel" :value="status.dictValue" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账号有效期" prop="validityDate">
                             <el-date-picker value-format="YYYY-MM-DD" v-model="newUser.validityDate" type="date" placeholder="账号有效期" format="YYYY-MM-DD" style="width: 100%"/>
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <div class="dialog-footer">
                            <el-button @click="cancleAddFn" class="!rounded-lg">取消</el-button>
                            <el-button type="primary" @click="addUser" class="!rounded-lg">保存</el-button>
                        </div>
                    </template>
                </el-dialog>
            </template>


            <!-- 编辑用户对话框 -->
            <el-dialog v-model="showEditUserDialog" title="编辑账号" width="500px" class="custom-dialog" destroy-on-close>
                <el-form :model="editingUser" label-width="100px" :rules="rules" ref="editUserForm" class="custom-form">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model="editingUser.username" disabled class="!rounded-lg" />
                    </el-form-item>
                    <el-form-item label="姓名" prop="name">
                        <el-input v-model="editingUser.name" class="!rounded-lg" />
                    </el-form-item>
                    
                    <el-form-item label="角色" prop="role">
                        <el-select v-model="editingUser.role" class="w-full !rounded-lg" style="width: 100%">
                            <el-option v-for="role in roleOptions" :key="role.roleId" :label="role.roleName"
                                :value="role.roleId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="editingUser.email" class="!rounded-lg" />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="editingUser.status" class="w-full !rounded-lg" style="width: 100%">
                            <el-option v-for="status in statusOptions" :key="status.dictLabel" :label="status.dictLabel"
                                :value="status.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="账号有效期" prop="validityDate">
                             <el-date-picker value-format="YYYY-MM-DD" v-model="editingUser.validityDate" type="date" placeholder="账号有效期" format="YYYY-MM-DD"  style="width: 100%"/>
                        </el-form-item>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="showEditUserDialog = false" class="!rounded-lg">取消</el-button>
                        <el-button type="primary" @click="updateUser" class="!rounded-lg">保存</el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 沙箱账号管理对话框 -->
            <el-dialog v-model="showSandboxDialog" :title="`${managingSandboxUser.nickName} - 绑定沙箱账号`" width="800px"
                class="custom-dialog" destroy-on-close @close="closeBtn">
                <div class="mb-6">
                    <h4 class="text-lg font-medium mb-4 text-gray-800">已绑定的沙箱账号</h4>
                    <el-table :data="managingSandboxUserList" style="width: 100%" class="custom-table"
                        v-loading='accountsLoading'>
                        <el-table-column prop="sandboxName" label="沙箱名称" min-width="150" />
                        <el-table-column prop="website" label="地址" min-width="250" />
                        <el-table-column prop="userName" label="账号" min-width="120" />
                        <el-table-column label="操作" width="100" fixed="right">
                            <template #default="{ row }">
                                <el-button link type="danger" @click="unbindSandboxAccount(row)" v-hasPermi="['system:user:sandboxUserBinding']" >解绑</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <div class="mb-6">
                    <h4 class="text-lg font-medium mb-4 text-gray-800">添加沙箱账号</h4>
                    <el-form :inline="true" class="custom-form">
                        <el-form-item label="沙箱账号" class="!mb-0">
                            <el-select clearable v-model="selectedSandboxAccountId" placeholder="请选择沙箱账号"
                                style="width: 300px" class="!rounded-lg">
                                <el-option v-for="account in availableSandboxAccounts" :key="account.sandboxUserId"
                                    :label="`${account.sandboxName} - ${account.userName}`"
                                    :value="account.sandboxUserId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="!mb-0">
                            <el-button type="primary" @click="bindSandboxAccount" class="!rounded-lg">绑定账号</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    getDictList,
    getdeptList,
    getroleList,
    getData,
    addAccount,
    editAccount,
    editStatus,
    resetPassword_,
    sandboxUserListFn, userBindingListFn,
    sandboxUserDeleteFn,
    sandboxUserAddSandBoxUserFn
} from '@/api/systemManagement/accountManagement.js'
import { status } from 'nprogress';
// 表单验证规则
const rules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    department: [{ required: true, message: '请选择部门', trigger: 'change' }],
    role: [{ required: true, message: '请选择角色', trigger: 'change' }],
    validityDate: [{ required: true, message: '请选择账号有效期', trigger: 'change' }],
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
};

// 加载状态
const loading = ref(false);

// 模拟数据 - 用户列表
const userRecords = ref([]);

// 搜索和筛选
const searchQuery = ref('');
const filters = reactive({
    department: undefined,
    role: '',
    status: ''
});


const closeBtn = () => {
    showSandboxDialog.value = false
    selectedSandboxAccountId.value = undefined
}

// 选项数据
const departmentOptions = ref([]);
const roleOptions = ref([]);
const statusOptions = ref([]);

// 应用筛选
const applyFilters = () => {
    currentPage.value = 1;
    fetchUserData()
};

// 重置筛选
const resetFilters = () => {
    filters.department = '';
    filters.role = '';
    filters.status = '';
    searchQuery.value = undefined
    currentPage.value = 1;
    fetchUserData()
};



// 用户表单相关
const showNewUserDialog = ref(false);
const showEditUserDialog = ref(false);
const newUserForm = ref(null);
const editUserForm = ref(null);


const newUser = reactive({
    username: undefined,
    name: undefined,
    password: undefined,
    department: undefined,
    role: undefined,
    email: undefined,
    status: undefined,
    validityDate: undefined
    // sandboxAccounts: []
});
const editingUser = reactive({
    username: undefined,
    name: undefined,
    password: undefined,
    department: undefined,
    role: undefined,
    email: undefined,
    status: undefined,
    userId: undefined,
    validityDate: undefined
    // sandboxAccounts: []
});


const addAccountFn = () => {
    // newUserForm.value.resetFields()
    showNewUserDialog.value = true


}

const cancleAddFn = () => {
    newUserForm.value.resetFields()
    showNewUserDialog.value = false
}


// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

const paginatedUsers = ref([])

const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchUserData()
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchUserData()
};

// 添加用户
const addUser = async () => {
    if (!newUserForm.value) return;

    await newUserForm.value.validate(async (valid) => {
        if (valid) {


            let params = {
                userName: newUser.username,
                nickName: newUser.name,
                // deptId: newUser.department,
                roleIds: newUser.role,
                email: newUser.email,
                status: newUser.status,
                password: newUser.password,
                validityDate: newUser.validityDate
            }

            addAccount(params).then(res => {
                if (res.code == 200) {
                    showNewUserDialog.value = false
                    ElMessage.success(`新建账号成功!`)
                    fetchUserData()
                    newUserForm.value.resetFields()
                }


            })
                .catch(err => {
                    ElMessage.error(`${error.message}`)
                })
        }
    });
};

// 编辑用户
const editUser = (user) => {

    showEditUserDialog.value = true;

    // let deptName_ = departmentOptions.value?.find(item => item.name == user.deptName)
    let role_ = roleOptions.value?.find(item => item.roleName == user.roleName)
    // department: undefined,
    // role: undefined,
    // console.log(user, 'user', deptName_, role_)
    editingUser.username = user.userName
    editingUser.name = user.nickName
    editingUser.email = user.email
    editingUser.userId = user.userId
    editingUser.status = user.status + ''
    // editingUser.department = deptName_.id
    editingUser.role = role_.roleId
    editingUser.validityDate = user.validityDate
    console.log(user, 'user')



};

// 更新用户
const updateUser = async () => {
    if (!editUserForm.value) return;

    await editUserForm.value.validate(async (valid) => {
        if (valid) {
            let params = {
                userName: editingUser.username,
                nickName: editingUser.name,
                // deptId: editingUser.department,
                roleIds: editingUser.role,
                email: editingUser.email,
                status: editingUser.status,
                password: editingUser.password,
                userId: editingUser.userId,
                validityDate: editingUser.validityDate
            }

            editAccount(params).then(res => {

                if (res.code == 200) {
                    showEditUserDialog.value = false
                    ElMessage.success(`更新账号成功!`)
                    fetchUserData()
                    editingUser.value.resetFields()
                }
            })
                .catch(err => {
                    ElMessage.error(`${error.message}`)
                })
        }
    });
};

// 禁用用户
const disableUser = (userId) => {
    ElMessageBox.confirm('确定要禁用该账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        editStatus({
            userId: userId,
            status: 1
        }).then(res => {
            if (res.code == 200) {
                fetchUserData()
                ElMessage.success('已禁用该账号,禁用状态的账号不能登录');
            }
        })
        // const user = userRecords.value.find(u => u.id === userId);
        // if (user) {
        //     user.status = '禁用';
        //     ElMessage.success('已禁用该账号');
        // }
    });
};

// 启用用户
const enableUser = (userId) => {
    ElMessageBox.confirm('确定要启用该账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // const user = userRecords.value.find(u => u.id === userId);
        // if (user) {
        //     user.status = '启用';
        //     ElMessage.success('已启用该账号');
        // }
        editStatus({
            userId: userId,
            status: 0
        }).then(res => {
            if (res.code == 200) {
                fetchUserData()
                ElMessage.success('已启用该账号');
            }
        })
    });
};

// 重置密码
const resetPassword = (userId) => {
    ElMessageBox.confirm('确定要重置该账号的密码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        resetPassword_({
            userId: userId
        }).then(res => {
            if (res.code == 200) {
                ElMessage.success('密码已重置为默认密码：SzZx@n#b!2025');
                fetchUserData()
            }

        })

    });
};

// 获取用户数据
const fetchUserData = async () => {
    dataLoading.value = true;
    try {
        let params = {
            userName: searchQuery.value,
            deptId: filters.department,
            roleId: filters.role,
            status: filters.status,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }

        console.log('获取用户账号数据...', params);
        getData(params).then(res => {
            if (res.code == 200) {
                paginatedUsers.value = res.rows
                totalItems.value = res.total
                dataLoading.value = false;
            }
            console.log(res)
        })
        // 在实际应用中应调用API
        // userRecords.value = await api.getUsers();
    } finally {

    }
};

// 沙箱账号绑定相关
const showSandboxDialog = ref(false);
const managingSandboxUser = ref({});
const selectedSandboxAccountId = ref('');
const managingSandboxUserList = ref([])
const accountsLoading = ref(false)
const dataLoading = ref(false)
// 所有可用的沙箱账号
const allSandboxAccounts = ref([
    { id: 1, sandboxId: 1, sandboxName: '信用评分沙箱', address: '*************:8080', username: 'sandbox_user1' },
    { id: 2, sandboxId: 2, sandboxName: '风险预警沙箱', address: 'sandbox.example.com', username: 'admin' },
    { id: 3, sandboxId: 3, sandboxName: '客户画像沙箱', address: '********:3000', username: 'developer' },
    { id: 4, sandboxId: 4, sandboxName: '欺诈检测沙箱', address: '********:3000', username: 'fraud_detector' },
    { id: 5, sandboxId: 5, sandboxName: '数据清洗沙箱', address: '********:3000', username: 'data_cleaner' },
]);

// 计算当前用户可绑定的沙箱账号
const availableSandboxAccounts = ref([])

// 沙箱账号管理
const manageSandboxAccounts = (user) => {
    managingSandboxUser.value = JSON.parse(JSON.stringify(user));
    showSandboxDialog.value = true
    console.log('managingSandboxUser.value', managingSandboxUser.value)

    accountsLoading.value = true
    sandboxUserListFn().then(res => {
        if (res.code == 200) {
            
            availableSandboxAccounts.value = res.data
        }
        console.log('sandboxUserListFn: ', res)
    })

    userBindingListFn({
        userId: user.userId
    }).then(res => {
        if (res.code == 200) {
            accountsLoading.value = false
            managingSandboxUserList.value = res.rows
        }
        console.log('userBindingListFn:', res)
    })
};



// 绑定沙箱账号
const bindSandboxAccount = () => {
    if (!selectedSandboxAccountId.value) {
        ElMessage.warning('请选择要绑定的沙箱账号');
        return;
    }
    accountsLoading.value = true
    sandboxUserAddSandBoxUserFn({
        userId: managingSandboxUser.value?.userId,
        sandboxUserId: selectedSandboxAccountId.value
    }).then(res => {
        if (res.code == 200) {
            ElMessage.success('绑定沙箱账号成功');
            
            selectedSandboxAccountId.value = undefined
            userBindingListFn({
                userId: managingSandboxUser.value?.userId,
            }).then(res => {
                if (res.code == 200) {
                    managingSandboxUserList.value = res.rows
                    accountsLoading.value = false
                }else {
                    accountsLoading.value = false
                }
                console.log('userBindingListFn:', res)
            }).catch(err => {
                console.log(111)
                accountsLoading.value = false
            })
        }
    }).catch(err => {
                console.log(111)
                accountsLoading.value = false
            })


};
const delLoading = ref(false)
// 解绑沙箱账号
const unbindSandboxAccount = (account) => {

    console.log(managingSandboxUser.value, account)
    ElMessageBox.confirm(`确定要解绑 ${account.sandboxName} 的账号吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        accountsLoading.value = true
        // delLoading.value = true
        sandboxUserDeleteFn({
            userId: managingSandboxUser.value?.userId,
            sandboxUserId: account.id
        }).then(res => {
            console.log(res, '解绑沙箱账号成功')
            if (res.code == 200) {
                //  delLoading.value = true
                ElMessage.success('解绑沙箱账号成功');
                userBindingListFn({
                    userId: managingSandboxUser.value?.userId,
                }).then(res => {
                    if (res.code == 200) {
                        managingSandboxUserList.value = res.rows
                        accountsLoading.value = false
                    }
                    console.log('userBindingListFn:', res)
                })

                sandboxUserListFn().then(res => {
                    if (res.code == 200) {
                        // accountsLoading.value = false
                        availableSandboxAccounts.value = res.data
                    }
                    console.log('sandboxUserListFn: ', res)
                })
            }
        })

    });
};

onMounted(() => {
    fetchUserData();

    getDictList({
        dictType: 'sys_user_status'
    }).then(res => {

        if (res.code == 200) {
            statusOptions.value = res.data
        }
        console.log(res)
    })
    // getdeptList().then(res => {
    //     if (res.code == 200) {
    //         departmentOptions.value = res.data
    //     }
    //     console.log(res, 'departmentOptions')
    // })
    getroleList().then(res => {
        if (res.code == 200) {
            roleOptions.value = res.data
        }
        console.log(res, 'getroleList')
    })
});
</script>

<style scoped>
.account-management {
    background-color: #f5f7fa;
}

.custom-table {
    --el-table-border-color: #ebeef5;
    --el-table-header-bg-color: #f5f7fa;
}

.custom-table :deep(.el-table__header) {
    font-weight: 600;
}

.custom-table :deep(.el-table__row) {
    transition: all 0.3s;
}

.custom-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
}

.custom-pagination {
    --el-pagination-button-bg-color: #fff;
    --el-pagination-hover-color: #409eff;
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
}

.custom-dialog :deep(.el-dialog__header) {
    margin-right: 0;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.custom-dialog :deep(.el-dialog__body) {
    padding: 24px;
}

.custom-dialog :deep(.el-dialog__footer) {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
}

.custom-form :deep(.el-form-item__label) {
    font-weight: 500;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.memory-alert {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.operation {
    display: flex;
}

@media (max-width: 768px) {
    .filter-form :deep(.el-form-item) {
        margin-bottom: 16px;
    }

    .custom-dialog {
        width: 90% !important;
        margin: 0 auto;
    }
}
</style>