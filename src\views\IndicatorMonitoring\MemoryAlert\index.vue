<template>
  <div class="memory-alert">
    <!-- 筛选区域 -->
    <h2>内存预警</h2>
    <el-card class="filter-card mb-4" style="padding-top: 16px;">
     

      <el-form :model="filters" label-width="100px" class="filter-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="服务器名称">
              <el-select v-model="filters.serverName" placeholder="请选择服务器" clearable class="w-full">
                <el-option v-for="server in serverOptions" :key="server" :label="server" :value="server" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实例名称">
              <el-input v-model="filters.instanceName" placeholder="请输入实例名称" clearable class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险类型">
              <el-select v-model="filters.riskType" placeholder="请选择风险类型" clearable class="w-full">
                <el-option v-for="type in riskTypeOptions" :key="type" :label="type" :value="type" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险等级">
              <el-select v-model="filters.riskLevel" placeholder="请选择风险等级" clearable class="w-full">
                <el-option v-for="level in riskLevelOptions" :key="level" :label="level" :value="level" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预警状态">
              <el-select v-model="filters.alertStatus" placeholder="请选择预警状态" clearable class="w-full">
                <el-option v-for="status in alertStatusOptions" :key="status" :label="status" :value="status" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="">
              
              <el-button @click="resetFilters">重置</el-button>
              <el-button type="primary" @click="applyFilters">查询</el-button>
            </el-form-item>
            
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 内存使用概览 -->
    <el-card class="overview-card mb-4">
      <template #header>
        <div class="card-header">
          <h3>内存使用概览</h3>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="stat-card main-stat">
            <h4>集群平均内存使用率</h4>
            <div class="stat-content">
              <span class="stat-value">{{ memoryStats.avgUsage }}</span>
              <el-progress 
                :percentage="parseInt(memoryStats.avgUsage)" 
                :color="getProgressColor(parseInt(memoryStats.avgUsage))"
                :stroke-width="12"
                class="custom-progress"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <h4>今日内存预警</h4>
            <div class="stat-content">
              <span class="stat-value warning">{{ memoryStats.todayAlerts }}</span>
              <div class="stat-icon warning">
                <i class="el-icon-warning"></i>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <h4>待处理预警</h4>
            <div class="stat-content">
              <span class="stat-value danger">{{ memoryStats.pendingAlerts }}</span>
              <div class="stat-icon danger">
                <i class="el-icon-error"></i>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-summary-card">
          <template #header>总预警数</template>
          <div class="stat-value primary">{{ stats.totalAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-summary-card">
          <template #header>高风险</template>
          <div class="stat-value danger">{{ stats.highRiskAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-summary-card">
          <template #header>中风险</template>
          <div class="stat-value warning">{{ stats.mediumRiskAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-summary-card">
          <template #header>低风险</template>
          <div class="stat-value success">{{ stats.lowRiskAlerts }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="paginatedAlerts" 
        style="width: 100%" 
        border 
        stripe
        highlight-current-row
        class="custom-table"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="serverName" label="服务器名称" min-width="120" />
        <el-table-column prop="instanceName" label="实例名称" min-width="120" />
        <el-table-column label="内存使用" min-width="150">
          <template #default="{ row }">
            <div class="memory-usage">
              <span>{{ row.memoryUsage }}</span>
              <el-progress 
                :percentage="parseInt(row.memoryUsage)" 
                :color="getProgressColor(parseInt(row.memoryUsage))"
                :stroke-width="8"
                class="table-progress"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="riskType" label="风险类型" min-width="120" />
        <el-table-column prop="riskLevel" label="风险等级" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getRiskLevelType(row.riskLevel)" effect="dark">{{ row.riskLevel }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="alertContent" label="预警内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="alertTime" label="预警时间" min-width="160" />
        <el-table-column prop="status" label="处理状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" effect="light">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" link @click="viewAlertDetail(row)">查看</el-button>
              <el-button 
                v-if="row.status !== '已处理' && row.status !== '已忽略'" 
                type="success" 
                link 
                @click="handleAlert(row.id)"
              >
                处理
              </el-button>
              <el-button 
                v-if="row.status !== '已忽略' && row.status !== '已处理'" 
                type="info" 
                link 
                @click="ignoreAlert(row.id)"
              >
                忽略
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[8, 16, 24, 32]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 内存使用统计
const memoryStats = reactive({
  avgUsage: '78%',
  todayAlerts: 12,
  pendingAlerts: 6
});

// 模拟数据 - 预警统计
const stats = reactive({
  totalAlerts: 48,
  highRiskAlerts: 10,
  mediumRiskAlerts: 22,
  lowRiskAlerts: 16
});

// 模拟数据 - 预警记录
const alertRecords = ref([
  { id: 1, serverName: 'server-prod-01', instanceName: 'mysql-master', memoryUsage: '96%', riskType: '内存溢出', riskLevel: '高', alertContent: '内存使用率超过95%，可能导致OOM', alertTime: '2024-05-20 09:30:00', status: '未处理' },
  { id: 2, serverName: 'server-prod-02', instanceName: 'mysql-slave-1', memoryUsage: '83%', riskType: '内存泄漏', riskLevel: '中', alertContent: '检测到内存持续上升，疑似存在内存泄漏', alertTime: '2024-05-20 08:15:00', status: '处理中' },
  { id: 3, serverName: 'server-prod-03', instanceName: 'redis-cache', memoryUsage: '91%', riskType: '内存溢出', riskLevel: '高', alertContent: '缓存服务内存即将耗尽', alertTime: '2024-05-19 23:45:00', status: '未处理' },
  { id: 4, serverName: 'server-prod-01', instanceName: 'elasticsearch-node1', memoryUsage: '87%', riskType: '内存分配异常', riskLevel: '中', alertContent: 'JVM堆内存分配异常', alertTime: '2024-05-19 16:30:00', status: '已处理' },
  { id: 5, serverName: 'server-prod-04', instanceName: 'tomcat-app', memoryUsage: '72%', riskType: '内存碎片化', riskLevel: '低', alertContent: '内存碎片化导致分配效率低下', alertTime: '2024-05-19 14:20:00', status: '已忽略' },
  { id: 6, serverName: 'server-prod-02', instanceName: 'mongodb-primary', memoryUsage: '89%', riskType: '内存溢出', riskLevel: '中', alertContent: '数据库服务内存使用率高', alertTime: '2024-05-19 10:15:00', status: '处理中' },
  { id: 7, serverName: 'server-prod-05', instanceName: 'rabbitmq', memoryUsage: '65%', riskType: '内存分配异常', riskLevel: '低', alertContent: '消息队列服务内存分配异常', alertTime: '2024-05-18 22:10:00', status: '已处理' },
  { id: 8, serverName: 'server-prod-03', instanceName: 'nginx-proxy', memoryUsage: '55%', riskType: '内存碎片化', riskLevel: '低', alertContent: '内存碎片率超过30%', alertTime: '2024-05-18 16:45:00', status: '已忽略' },
  { id: 9, serverName: 'server-prod-01', instanceName: 'mysql-master', memoryUsage: '93%', riskType: '内存溢出', riskLevel: '高', alertContent: '内存使用率持续上升，当前超过90%', alertTime: '2024-05-18 12:30:00', status: '处理中' },
  { id: 10, serverName: 'server-prod-06', instanceName: 'kafka-broker', memoryUsage: '78%', riskType: '内存泄漏', riskLevel: '中', alertContent: '疑似存在内存泄漏，48小时内增长15%', alertTime: '2024-05-18 09:00:00', status: '未处理' },
  { id: 11, serverName: 'server-prod-02', instanceName: 'mysql-slave-2', memoryUsage: '84%', riskType: '内存分配异常', riskLevel: '中', alertContent: '内存分片分配异常', alertTime: '2024-05-17 23:20:00', status: '处理中' },
  { id: 12, serverName: 'server-prod-05', instanceName: 'spark-worker', memoryUsage: '95%', riskType: '内存溢出', riskLevel: '高', alertContent: '计算节点内存使用率过高', alertTime: '2024-05-17 18:15:00', status: '未处理' },
]);

// 筛选条件
const filters = reactive({
  serverName: '',
  instanceName: '',
  riskType: '',
  riskLevel: '',
  alertStatus: ''
});

// 选项数据
const serverOptions = ['server-prod-01', 'server-prod-02', 'server-prod-03', 'server-prod-04', 'server-prod-05', 'server-prod-06'];
const riskTypeOptions = ['内存溢出', '内存泄漏', '内存分配异常', '内存碎片化'];
const riskLevelOptions = ['高', '中', '低'];
const alertStatusOptions = ['未处理', '处理中', '已处理', '已忽略'];

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage > 90) return '#F56C6C';
  if (percentage > 75) return '#E6A23C';
  return '#67C23A';
};

// 获取风险等级标签类型
const getRiskLevelType = (level) => {
  const types = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  };
  return types[level] || 'info';
};

// 获取状态标签类型
const getStatusType = (status) => {
  const types = {
    '未处理': 'danger',
    '处理中': 'warning',
    '已处理': 'success',
    '已忽略': 'info'
  };
  return types[status] || 'info';
};

// 应用筛选
const applyFilters = () => {
  currentPage.value = 1;
};

// 重置筛选
const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  currentPage.value = 1;
};

// 查看预警详情
const viewAlertDetail = (alert) => {
  ElMessageBox.alert(
    `服务器名称: ${alert.serverName}\n实例名称: ${alert.instanceName}\n内存使用率: ${alert.memoryUsage}\n风险类型: ${alert.riskType}\n风险等级: ${alert.riskLevel}\n预警内容: ${alert.alertContent}\n预警时间: ${alert.alertTime}\n状态: ${alert.status}`,
    '预警详情',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  );
};

// 处理预警
const handleAlert = (alertId) => {
  ElMessageBox.confirm(
    `确定要处理ID为 "${alertId}" 的预警吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const alert = alertRecords.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = '处理中';
      ElMessage.success('操作成功');
    }
  }).catch(() => {});
};

// 忽略预警
const ignoreAlert = (alertId) => {
  ElMessageBox.confirm(
    `确定要忽略ID为 "${alertId}" 的预警吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const alert = alertRecords.value.find(a => a.id === alertId);
    if (alert) {
      alert.status = '已忽略';
      ElMessage.success('操作成功');
    }
  }).catch(() => {});
};

// 过滤预警记录
const filteredAlerts = computed(() => {
  return alertRecords.value.filter(alert => {
    const serverNameMatch = !filters.serverName || alert.serverName === filters.serverName;
    const instanceNameMatch = !filters.instanceName || alert.instanceName.toLowerCase().includes(filters.instanceName.toLowerCase());
    const riskTypeMatch = !filters.riskType || alert.riskType === filters.riskType;
    const riskLevelMatch = !filters.riskLevel || alert.riskLevel === filters.riskLevel;
    const statusMatch = !filters.alertStatus || alert.status === filters.alertStatus;
    return serverNameMatch && instanceNameMatch && riskTypeMatch && riskLevelMatch && statusMatch;
  });
});

// 分页
const currentPage = ref(1);
const pageSize = ref(8);
const totalItems = computed(() => filteredAlerts.value.length);

const paginatedAlerts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredAlerts.value.slice(start, end);
});

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 获取预警数据
const fetchAlertData = async () => {
  console.log('获取内存预警数据...');
  // 在实际应用中应调用API
  // const response = await api.getMemoryAlerts();
  // alertRecords.value = response.data;
  // memoryStats.avgUsage = response.stats.avgUsage;
  // 等等...
};

onMounted(() => {
  fetchAlertData();
});
</script>

<style scoped>
.memory-alert {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.mb-4 {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.filter-form {
  margin-bottom: 0;
}

.w-full {
  width: 100%;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stat-card {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.main-stat {
  background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
}

.stat-card h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon.danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-value.primary {
  color: #409EFF;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.warning {
  color: #E6A23C;
}

.stat-value.danger {
  color: #F56C6C;
}

.stat-summary-card {
  height: 100%;
  transition: all 0.3s ease;
}

.stat-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.table-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.custom-table {
  margin: 0;
}

.custom-table :deep(th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.memory-usage {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-progress {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}

:deep(.el-table .cell) {
  white-space: nowrap;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}
</style> 