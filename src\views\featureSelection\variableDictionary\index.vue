<template>
    <div class="variable-dictionary">
        <!-- 筛选区域 -->
        <el-card class="filter-card">
            <template #header>
                <div class="card-header">
                    <span class="header-title">变量筛选</span>
                </div>
            </template>
            <el-form :model="filterForm" label-width="100px" class="filter-form">
                <el-row :gutter="24">
                    <el-col :span="6">
                        <el-form-item label="一级维度">
                            <el-select v-model="filterForm.firstDimension" placeholder="请选择" class="w-full" clearable
                                @change="disabled1Handle">
                                <el-option v-for="option in firstDimensionOptions" :key="option.dictLabel"
                                    :label="option.dictLabel" :value="option.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="二级维度">
                            <el-select v-model="filterForm.secondDimension" placeholder="请选择" class="w-full" clearable
                                :disabled="!filterForm.firstDimension" @change="disabled2Handle">
                                <el-option v-for="option in secondDimensionOptions" :key="option.dictLabel"
                                    :label="option.dictLabel" :value="option.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="三级维度">
                            <el-select v-model="filterForm.thirdDimension" placeholder="请选择" class="w-full" clearable
                                :disabled="!filterForm.secondDimension || !filterForm.firstDimension">
                                <el-option v-for="option in thirdDimensionOptions" :key="option.dictLabel"
                                    :label="option.dictLabel" :value="option.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="变量类型">
                            <el-select v-model="filterForm.variableType" placeholder="请选择" class="w-full" clearable>
                                <el-option v-for="option in variableTypeOptions" :key="option.dictLabel"
                                    :label="option.dictLabel" :value="option.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="6">
                        <el-form-item label="中文名">
                            <el-input v-model="filterForm.cnName" placeholder="请输入中文名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="英文名">
                            <el-input v-model="filterForm.enName" placeholder="请输入英文名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="回溯状态">
                            <el-select v-model="filterForm.backTrackType" placeholder="请选择" class="w-full" clearable>
                                <el-option v-for="option in huisuo_statu_options" :key="option.dictLabel"
                                    :label="option.dictLabel" :value="option.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">

                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item class="action-buttons" style="margin-left: -70px;">
                            <el-button type="primary" @click="handleSearch" :icon="Search"
                                v-hasPermi="['filter:varDict:page']">查询</el-button>
                            <el-button @click="handleReset" :icon="Refresh"
                                v-hasPermi="['filter:varDict:page']">重置</el-button>
                            <el-button type="success" @click="handleDownload" :icon="Download"
                                v-hasPermi="['filter:varDict:export']">下载</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>


        <el-card class="filter-card" v-if="showVariableDictCard">
            <template #header>
                <div class="card-header">
                    <span class="header-title">变量字典管理</span>
                </div>
            </template>
            <el-row>
                <el-col>
                    <el-form-item class="action-buttons" style="margin-left: 30px;">
                        <el-button type="primary" @click="handleImportVariables"
                            v-hasPermi="['filter:varDict:maintenance:upload']">
                            <el-icon><el-icon>
                                    <Upload />
                                </el-icon></el-icon>上传
                        </el-button>
                        <el-button type="success" @click="handleDownload_" :icon="Download"
                            v-hasPermi="['filter:varDict:maintenance:export']">下载</el-button>
                        <input id="import-files" type="file" accept=".xlsx,.xls" class="hidden"
                            @change="handleFileChange" style="display: none" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-card>

        <!-- 数据表格 -->
        <el-card class="table-card">
            <template #header>
                <div class="card-header">
                    <span class="header-title">变量列表</span>
                    <span class="total-count">共 {{ total }} 条数据</span>
                </div>
            </template>
            <!-- 一级维度、二级维度、三级维度、中文名、英文名、回溯状态、变量格式、最远追溯时间、最近追溯时间、平均覆盖度 -->
            <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe highlight-current-row>
                <el-table-column type="index" label="序号" width="80" align="center" />

                <el-table-column prop="firstDimension" label="一级维度" min-width="120" show-overflow-tooltip />
                <el-table-column prop="secondDimension" label="二级维度" min-width="120" show-overflow-tooltip />
                <el-table-column prop="thirdDimension" label="三级维度" min-width="120" show-overflow-tooltip />
                <!-- <el-table-column prop="varId" label="ID" width="120" align="center" /> -->
                <!-- <el-table-column prop="varType" label="变量类型" width="100" align="center" /> -->
                <el-table-column prop="chName" label="中文名" min-width="150" show-overflow-tooltip />
                <el-table-column prop="enName" label="英文名" min-width="150" show-overflow-tooltip />

                <el-table-column prop="backTrackType" label="回溯状态" min-width="150" show-overflow-tooltip />
                <el-table-column prop="varFormat" label="变量格式" min-width="150" show-overflow-tooltip />
                <el-table-column prop="earliestTime" label="最远追溯时间" min-width="150" show-overflow-tooltip />
                <el-table-column prop="latestTime" label="最近追溯时间" min-width="150" show-overflow-tooltip />
                <el-table-column prop="coverage" label="平均覆盖度(%)" min-width="150" show-overflow-tooltip />

                <el-table-column label="操作" width="150" fixed="right" align="center">
                    <template #default="{ row }">
                        <div class="operation-btn">
                            <el-button type="primary" link @click="viewDetail(row)"
                                v-hasPermi="['filter:varDict:page']">查看</el-button>

                            <el-button type="primary" link @click="editDetail(row)"
                                v-hasPermi="['filter:varDict:edit']">编辑</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="mt-6 flex justify-end">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                    :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" class="custom-pagination" />
            </div>
        </el-card>

        <!-- 
        ID：不能修改，管理员也不能修改，且不能重复
        更新频率：日更、月更、年更
        变量密级：公开、保密
        数据来源：公共、商业、金融、混合
        变量格式：数值型、字符串、布尔型
        开发状态：已开发、未开发
        当前状态：未上架、已上架、已下架
        变量类型：一级变量、二级变量、标签变量 
        -->

        <!-- 编辑对话框 -->
        <el-dialog v-model="showEditRoleDialog" title="编辑变量" width="600px" destroy-on-close class="role-dialog">
            <el-form :model="editvarForm" :rules="rules2" label-width="130px" class="px-4" ref="editvarFormRef">
                <el-form-item label="ID" prop="varId">
                    <el-input maxlength="200" v-model="editvarForm.varId" placeholder="请输入ID" disabled />
                </el-form-item>
                <el-form-item label="一级维度" prop="firstDimension">
                    <el-input maxlength="200" v-model="editvarForm.firstDimension" placeholder="请输入一级维度" />
                </el-form-item>
                <el-form-item label="二级维度" prop="secondDimension">
                    <el-input maxlength="200" v-model="editvarForm.secondDimension" placeholder="请输入二级维度" />
                </el-form-item>
                <el-form-item label="三级维度" prop="thirdDimension">
                    <el-input maxlength="200" v-model="editvarForm.thirdDimension" placeholder="请输入三级维度" />
                </el-form-item>
                <el-form-item label="中文名" prop="chName">
                    <el-input maxlength="200" v-model="editvarForm.chName" placeholder="请输入中文名" />
                </el-form-item>
                <el-form-item label="英文名" prop="enName">
                    <el-input maxlength="200" v-model="editvarForm.enName" placeholder="请输入英文名" />
                </el-form-item>
                <el-form-item label="变量类型" prop="varType">
                    <el-select v-model="editvarForm.varType" placeholder="请选择变量类型" class="w-full" clearable>
                        <el-option v-for="option in variableTypeOptions" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                    <!-- <el-input maxlength="200" v-model="editvarForm.varType" placeholder="请输入变量类型" /> -->
                </el-form-item>

                <el-form-item label="回溯状态" prop="backTrackType">
                    <!-- <el-input maxlength="200" v-model="editvarForm.backTrackType" placeholder="请输入回溯状态" /> -->
                    <el-select v-model="editvarForm.backTrackType" placeholder="请选择回溯状态" class="w-full" clearable>
                        <el-option v-for="option in huisuo_statu_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="最远追溯时间" prop="earliestTime">
                    <el-input maxlength="200" v-model="editvarForm.earliestTime" placeholder="请输入最远追溯时间" />
                </el-form-item>
                <el-form-item label="最近追溯时间" prop="latestTime">
                    <el-input maxlength="200" v-model="editvarForm.latestTime" placeholder="请输入最近追溯时间" />
                </el-form-item>

                <el-form-item label="平均覆盖度" prop="coverage">
                    <el-input maxlength="200" v-model="editvarForm.coverage" placeholder="请输入平均覆盖度">
                        <template #append>%</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="调用次数" prop="callCount">
                    <el-input maxlength="200" v-model="editvarForm.callCount" placeholder="请输入生产调用次数" />
                </el-form-item>
                <el-form-item label="更新频率" prop="updateFrequency">
                    <!-- <el-input maxlength="200" v-model="editvarForm.updateFrequency" placeholder="请输入更新频率" /> -->
                    <el-select v-model="editvarForm.updateFrequency" placeholder="请选择" class="w-full" clearable>
                        <el-option v-for="option in update_frequency_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="变量密级" prop="confidentialityLevel">
                    <!-- <el-input maxlength="200" v-model="editvarForm.confidentialityLevel" placeholder="请输入变量密级" /> -->
                    <el-select v-model="editvarForm.confidentialityLevel" placeholder="请选择变量密级" class="w-full"
                        clearable>
                        <el-option v-for="option in variable_confidentiality_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="存储过程名称" prop="storedProcedures">
                    <el-input maxlength="200" v-model="editvarForm.storedProcedures" placeholder="请输入存储过程名称" />
                </el-form-item>
                <el-form-item label="表名称" prop="tName">
                    <el-input maxlength="200" v-model="editvarForm.sourceTable" placeholder="请输入表名称" />
                </el-form-item>
                <el-form-item label="变量格式" prop="varFormat">
                    <!-- <el-input maxlength="200" v-model="editvarForm.varFormat" placeholder="请输入变量格式" /> -->
                    <el-select v-model="editvarForm.varFormat" placeholder="请选择变量格式" class="w-full" clearable>
                        <el-option v-for="option in variable_format_options" :key="option.label" :label="option.label"
                            :value="option.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="业务含义" prop="bizMean">
                    <el-input v-model="editvarForm.bizMean" placeholder="请输入业务含义" maxlength="500" />
                </el-form-item>
                <el-form-item label="变量加工逻辑" prop="proLogic">
                    <el-input maxlength="500" v-model="editvarForm.proLogic" placeholder="请输入变量加工逻辑" />
                </el-form-item>

                <el-form-item label="变量加工时间" prop="proTime">
                    <el-input maxlength="200" v-model="editvarForm.proTime" placeholder="请输入变量加工时间" />
                </el-form-item>


                <el-form-item label="变量版本" prop="version">
                    <el-input maxlength="200" v-model="editvarForm.version" placeholder="请输入版本" disabled />
                </el-form-item>
                <el-form-item label="开发状态" prop="devStatus">
                    <!-- <el-input maxlength="200" v-model="editvarForm.devStatus" placeholder="请输入开发状态" /> -->
                    <el-select v-model="editvarForm.devStatus" placeholder="请选择开发状态" class="w-full" clearable>
                        <el-option v-for="option in kaifa_status_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>

                <el-form-item label="当前状态" prop="currentStatus">
                    <!-- <el-input maxlength="200" v-model="editvarForm.currentStatus" placeholder="请输入当前状态" /> -->
                    <el-select v-model="editvarForm.currentStatus" placeholder="请选择当前状态" class="w-full" clearable>
                        <el-option v-for="option in danqian_status_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="数据来源" prop="dataSource">
                    <!-- <el-input maxlength="200" v-model="editvarForm.dataSource" placeholder="请输入数据来源" /> -->
                    <el-select v-model="editvarForm.dataSource" placeholder="请选择数据来源" class="w-full" clearable>
                        <el-option v-for="option in dataSource_options" :key="option.dictLabel"
                            :label="option.dictLabel" :value="option.dictValue" />
                    </el-select>
                </el-form-item>


                <el-form-item label="备注" prop="remark">
                    <el-input maxlength="500" type="textarea" v-model="editvarForm.remark" placeholder="请输入备注" />
                </el-form-item>

                <el-form-item label="逻辑制定账号" prop="logicFormulation">
                    <el-input maxlength="200" v-model="editvarForm.logicFormulation" placeholder="请输入逻辑制定账号" />
                </el-form-item>
                <el-form-item label="变量开发账号" prop="process">
                    <el-input maxlength="200" v-model="editvarForm.process" placeholder="请输入变量开发账号" />
                </el-form-item>
                <el-form-item label="变量维护账号" prop="maintainer">
                    <el-input maxlength="200" v-model="editvarForm.maintainer" placeholder="请输入维护人" />
                </el-form-item>





                <!-- <el-form-item label="来源表" prop="sourceTable">
                    <el-input maxlength="200" v-model="editvarForm.sourceTable" placeholder="请输入来源表" />
                </el-form-item> -->



                <!-- <el-form-item label="逻辑制定" prop="logicFormulation">
                    <el-input maxlength="200" v-model="editvarForm.logicFormulation" placeholder="请输入逻辑制定" />
                </el-form-item>

                <el-form-item label="加工人" prop="process">
                    <el-input maxlength="200" v-model="editvarForm.process" placeholder="请输入加工人" />
                </el-form-item> -->













            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showEditRoleDialog = false">取消</el-button>
                    <el-button type="primary" @click="updateRole">保存</el-button>
                </div>
            </template>
        </el-dialog>


    </div>
</template>

<script setup>
    import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue'
    import { useRouter } from 'vue-router'
    // import { variableApi } from '../utils/api'
    import { ElMessage } from 'element-plus'
    import auth from '@/plugins/auth'
    import { Search, Refresh, Download } from '@element-plus/icons-vue'
    import {
        getVarDictList,
        getDataList,
        exportDown,
        exportDown_,
        editFm,
        getfirstDimensionOptions,
        getsecondDimensionOptions,
        getthirdDimensionOptions
    } from '@/api/featureSelection/variableDictionary.js'
    import { dayjs } from 'element-plus'
    import axios from 'axios'
    const { proxy } = getCurrentInstance();


    // 权限检查计算属性
    const hasUploadPermission = computed(() => {
        return auth.hasPermi('filter:varDict:maintenance:upload')
    })

    const hasDownloadPermission = computed(() => {
        return auth.hasPermi('filter:varDict:maintenance:export')
    })

    // 检查是否显示变量字典卡片（至少有一个权限）
    const showVariableDictCard = computed(() => {
        return hasUploadPermission.value || hasDownloadPermission.value
    })
    const disabled1 = ref(true)
    const disabled2 = ref(true)

    const huisuo_statu_options = ref([
        // {
        //     label: '可回溯',
        //     value: '可回溯'
        // },
        //  {
        //     label: '不可回溯',
        //     value: '不可回溯'
        // },
    ])
    // 引入中文包
    // import 'dayjs/locale/zh-cn'
    // dayjs.locale('zh-cn') // 设置中文
    const router = useRouter()
    const showEditRoleDialog = ref(false)
    // 加载状态
    const loading = ref(false)

    const editvarFormRef = ref()

    // 筛选条件
    const filterForm = reactive({
        firstDimension: '',
        secondDimension: '',
        thirdDimension: '',
        variableType: '',
        cnName: '',
        enName: ''
    })

    const editvarForm = reactive({
        firstDimension: '',
        secondDimension: '',
        thirdDimension: '',
        varType: '',
        chName: '',
        enName: '',
        backTrackType: '',
        bizMean: '',
        dataSource: '',
        updateFrequency: '',
        confidentialityLevel: '',
        earliestTime: '',
        latestTime: '',
        version: '',
        devStatus: '',
        currentStatus: '',
        varId: '',
        proLogic: '',
        proTime: '',
        sourceTable: '',
        callCount: '',
        logicFormulation: '',
        process: '',
        maintainer: '',
        logicFormulation: '',
        process: '',
        storedProcedures: '',
        tName: '',
        varFormat: '',
        remark: '',
        id: '',
        coverage: ''
    })

    // 更新频率：日更、月更、年更
    // 变量密级：公开、保密
    // 数据来源：公共、商业、金融、混合
    // 变量格式：数值型、字符串、布尔型
    // 开发状态：已开发、未开发
    // 当前状态：未上架、已上架、已下架
    // 变量类型：一级变量、二级变量、标签变量 
    const update_frequency_options = ref([
        {
            label: '日更',
            value: '日更'
        },
        {
            label: '月更',
            value: '月更'
        },
        {
            label: '年更',
            value: '年更'
        }
    ])

    const variable_confidentiality_options = ref([
        {
            label: '公开',
            value: '公开'
        },
        {
            label: '商业',
            value: '商业'
        },
        {
            label: '金融',
            value: '金融'
        },
        {
            label: '混合',
            value: '混合'
        },
    ])

    const dataSource_options = ref([
        {
            label: '公共',
            value: '公共'
        },
        {
            label: '保密',
            value: '保密'
        },
    ])

    const variable_format_options = ref([
        {
            label: '数值型',
            value: '数值型'
        }, {
            label: '字符串',
            value: '字符串'
        }, {
            label: '布尔型',
            value: '布尔型'
        },
    ])

    const kaifa_status_options = ref([
        {
            label: '已开发',
            value: '已开发'
        },
        {
            label: '未开发',
            value: '未开发'
        },
    ])

    const danqian_status_options = ref([
        {
            label: '未上架',
            value: '未上架'
        },
        {
            label: '已上架',
            value: '已上架'
        },
        {
            label: '已下架',
            value: '已下架'
        },
    ])

    const var_type_options = ref([
        {
            label: '一级变量',
            value: '一级变量'
        },
        {
            label: '二级变量',
            value: '二级变量'
        },
        {
            label: '标签变量',
            value: '标签变量'
        },
    ])


    // 一级维度选项
    const firstDimensionOptions = ref('')

    // 二级维度选项
    const secondDimensionOptions = ref('')

    // 三级维度选项
    const thirdDimensionOptions = ref('')

    // 变量类型选项
    const variableTypeOptions = ref([])

    // 表格数据
    const tableData = ref([])

    // 分页相关
    // 分页
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    const rules2 = {
        // firstDimension: [{ required: true, message: '请输入一级维度', trigger: 'blur' }],
        // secondDimension: [{ required: true, message: '请输入二级维度', trigger: 'blur' }],

        // thirdDimension: [{ required: true, message: '请输入三级维度', trigger: 'blur' }],
        // varType: [{ required: true, message: '请输入变量类型', trigger: 'blur' }],
        // chName: [{ required: true, message: '请输入中文名', trigger: 'blur' }],

        // enName: [{ required: true, message: '请输入英文名', trigger: 'blur' }],
        // backTrackType: [{ required: true, message: '请输入回溯状态', trigger: 'blur' }],
        // bizMean: [{ required: true, message: '请输入业务含义', trigger: 'blur' }],

        // dataSource: [{ required: true, message: '请输入数据来源', trigger: 'blur' }],
        // updateFrequency: [{ required: true, message: '请输入更新频率', trigger: 'blur' }],
        // confidentialityLevel: [{ required: true, message: '请输入变量密级', trigger: 'blur' }],

        // earliestTime: [{ required: true, message: '请输入最远追溯时间', trigger: 'blur' }],
        // latestTime: [{ required: true, message: '请输入最近追溯时间', trigger: 'blur' }],


        // version: [{ required: true, message: '请输入版本', trigger: 'blur' }],
        // devStatus: [{ required: true, message: '请输入开发状态', trigger: 'blur' }],


        // currentStatus: [{ required: true, message: '请输入当前状态', trigger: 'blur' }],
        // varId: [{ required: true, message: '请输入ID', trigger: 'blur' }],

        // proLogic: [{ required: true, message: '请输入变量加工逻辑', trigger: 'blur' }],
        // proTime: [{ required: true, message: '请输入变量加工时间', trigger: 'blur' }],

        // sourceTable: [{ required: true, message: '请输入来源表', trigger: 'blur' }],
        // callCount: [{ required: true, message: '请输入生产调用次数', trigger: 'blur' }],
        // logicFormulation: [{ required: true, message: '请输入逻辑制定', trigger: 'blur' }],
        // process: [{ required: true, message: '请输入加工人', trigger: 'blur' }],

        // maintainer: [{ required: true, message: '请输入维护人', trigger: 'blur' }],
        // logicFormulation: [{ required: true, message: '请输入逻辑制定账号', trigger: 'blur' }],
        // process: [{ required: true, message: '请输入变量开发账号', trigger: 'blur' }],
        // storedProcedures: [{ required: true, message: '请输入存储过程名称', trigger: 'blur' }],
        // tName: [{ required: true, message: '请输入表名称', trigger: 'blur' }],
        // varFormat: [{ required: true, message: '请输入变量格式', trigger: 'blur' }],
        // remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],



    }


    // 上传
    const handleImportVariables = () => {
        const fileInput = document.getElementById("import-files");
        fileInput.value = "";

        fileInput.click();
    };




    const handleSizeChange = (val) => {
        pageSize.value = val;
        currentPage.value = 1;
        let params = {
            firstDimension: filterForm.firstDimension,
            secondDimension: filterForm.secondDimension,
            thirdDimension: filterForm.thirdDimension,
            varType: filterForm.variableType,
            chName: filterForm.cnName,
            enName: filterForm.enName,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }
        localStorage.setItem('params', JSON.stringify(params))
        fetchVariables()
    };

    const handleCurrentChange = (val) => {
        currentPage.value = val;
        let params = {
            firstDimension: filterForm.firstDimension,
            secondDimension: filterForm.secondDimension,
            thirdDimension: filterForm.thirdDimension,
            varType: filterForm.variableType,
            chName: filterForm.cnName,
            enName: filterForm.enName,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }
        localStorage.setItem('params', JSON.stringify(params))
        fetchVariables()
    };



    const disabled1Handle = val => {
        if (!val) {
            filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined

        } else {
            filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined
            getsecondDimensionOptions({
                firstDimension: val
            }).then(res => {
                if (res.code == 200) {
                    secondDimensionOptions.value = res.data
                    console.log(res)
                }
            }).catch(err => {
                console.log('err', err)
            })
        }
    }

    const disabled2Handle = val => {
        if (!val) {
            filterForm.thirdDimension = undefined

        } else {
            // filterForm.secondDimension = undefined
            filterForm.thirdDimension = undefined
            getthirdDimensionOptions({
                secondDimension: val
            }).then(res => {
                if (res.code == 200) {
                    thirdDimensionOptions.value = res.data
                    console.log(res)
                }
            }).catch(err => {
                console.log('err', err)
            })

        }
    }

    // 获取数据表格
    const fetchVariables = async () => {
        loading.value = true
        try {

            let params = {
                firstDimension: filterForm.firstDimension,
                secondDimension: filterForm.secondDimension,
                thirdDimension: filterForm.thirdDimension,
                varType: filterForm.variableType,
                backTrackType: filterForm.backTrackType,
                chName: filterForm.cnName,
                enName: filterForm.enName,
                pageNum: currentPage.value,
                pageSize: pageSize.value
            }


            getVarDictList(params).then(res => {
                if (res.code == 200) {
                    tableData.value = res.rows
                    total.value = res.total
                    loading.value = false
                    console.log('获取api数据', res)
                }
            })
        } catch (error) {
            tableData.value = []
            console.error('获取变量列表失败:', error)
        } finally {

        }
    }

    // 查询方法
    const handleSearch = async () => {
        loading.value = true
        let params = {
            firstDimension: filterForm.firstDimension,
            secondDimension: filterForm.secondDimension,
            thirdDimension: filterForm.thirdDimension,
            varType: filterForm.variableType,
            chName: filterForm.cnName,
            enName: filterForm.enName,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        }
        localStorage.setItem('params', JSON.stringify(params))

        try {
            fetchVariables()
            // total.value = filteredData.value.length
            // currentPage.value = 1
        } catch (error) {
            tableData.value = []
            console.error('筛选变量失败:', error)
        } finally {
            loading.value = false
        }
    }

    // 文件选择变化
    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            handleUpload(file);
            e.target.value = "";
        }
    };


    // 处理文件上传
    const handleUpload = (file) => {
        if (!file) return;

        const formData = new FormData();
        formData.append("file", file);

        let config = {
            headers: {
                "Content-Type": "multipart/form-data",
                Authorization: "Bearer " + localStorage.getItem("Admin-Token"),
            },
        };

        // 显示全屏加载动画
        proxy.$modal.loading("文件上传中，请稍候...");

        axios
            .post(
                import.meta.env.VITE_APP_BASE_API + "/biz/maintenance/upload",
                formData,
                config
            )
            .then((res) => {
                console.log(res);
                if (res.data.code == 200) {

                    ElMessage.success("文件上传成功");




                } else {
                    ElMessage.error(res.data.msg);
                }
            })
            .catch((error) => {
                console.error("Upload error:", error);
                ElMessage.error(error.msg);
                fileList.value = [];
            })
            .finally(() => {
                // 关闭全屏加载动画
                proxy.$modal.closeLoading();
            });
    };


    // 下载方法
    // const handleDownload = () => {
    //     ElMessage.info('下载功能待实现')


    // }
    // 更新
    const updateRole = async () => {
        // if (!editingRole.name) {
        //     ElMessage.warning('请输入角色名称');
        //     return;
        // }

        await editvarFormRef.value.validate(async (valid) => {
            if (valid) {

                let params = {
                    firstDimension: editvarForm.firstDimension,
                    secondDimension: editvarForm.secondDimension,
                    thirdDimension: editvarForm.thirdDimension,
                    varType: editvarForm.varType,
                    chName: editvarForm.chName,
                    enName: editvarForm.enName,
                    backTrackType: editvarForm.backTrackType,
                    bizMean: editvarForm.bizMean,
                    dataSource: editvarForm.dataSource,
                    updateFrequency: editvarForm.updateFrequency,
                    confidentialityLevel: editvarForm.confidentialityLevel,
                    earliestTime: editvarForm.earliestTime,
                    latestTime: editvarForm.latestTime,
                    version: editvarForm.version,
                    devStatus: editvarForm.devStatus,
                    currentStatus: editvarForm.currentStatus,
                    varId: editvarForm.varId,
                    proLogic: editvarForm.proLogic,
                    proTime: editvarForm.proTime,
                    sourceTable: editvarForm.sourceTable,
                    callCount: editvarForm.callCount,
                    logicFormulation: editvarForm.logicFormulation,
                    process: editvarForm.process,
                    maintainer: editvarForm.maintainer,
                    logicFormulation: editvarForm.logicFormulation,
                    process: editvarForm.process,
                    storedProcedures: editvarForm.storedProcedures,
                    tName: editvarForm.tName,
                    varFormat: editvarForm.varFormat,
                    remark: editvarForm.remark,
                    id: editvarForm.id,
                    coverage: editvarForm.coverage,
                }


                console.log('params:', params)
                editFm(params).then(res => {
                    if (res.code == 200) {

                        showEditRoleDialog.value = false;
                        ElMessage.success('编辑成功');
                        fetchVariables()
                    }
                }).catch(err => {
                    console.log(err, 'err')
                })

            }
        });




    };

    /** 下载按钮操作 */
    const handleDownload = async (item) => {
        let params = {
            firstDimension: filterForm.firstDimension,
            secondDimension: filterForm.secondDimension,
            thirdDimension: filterForm.thirdDimension,
            varType: filterForm.variableType,
            chName: filterForm.cnName,
            enName: filterForm.enName,
            // pageNum: currentPage.value,
            // pageSize: pageSize.value
        }
        //文件后缀
        //   const fileExtension = item.filePath.slice(item.filePath.lastIndexOf('.') + 1)
        try {
            let result = await exportDown(params);
            console.log(result)

            const blob = new Blob([result], {
                type: 'application/vnd.ms-excel',
            })
            let date = new Date()
            date = dayjs(date).format('YYYY-MM-DD HH:mm:ss')
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `变量列表-${date}.xlsx`
            a.click()
            window.URL.revokeObjectURL(url)
        } catch (error) {
            // console.log('error: ', err)
        }
    }

    const handleDownload_ = async (item) => {
        let params = {
            // firstDimension: filterForm.firstDimension,
            // secondDimension: filterForm.secondDimension,
            // thirdDimension: filterForm.thirdDimension,
            // varType: filterForm.variableType,
            // chName: filterForm.cnName,
            // enName: filterForm.enName,
            // pageNum: currentPage.value,
            // pageSize: pageSize.value
        }
        //文件后缀
        //   const fileExtension = item.filePath.slice(item.filePath.lastIndexOf('.') + 1)
        try {
            let result = await exportDown_(params);
            console.log(result)

            const blob = new Blob([result], {
                type: 'application/vnd.ms-excel',
            })
            let date = new Date()
            date = dayjs(date).format('YYYY-MM-DD HH:mm:ss')
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `变量字典-${date}.xlsx`
            a.click()
            window.URL.revokeObjectURL(url)
        } catch (error) {
            // console.log('error: ', err)
        }
    }

    // 查看变量详情
    const viewDetail = (row) => {
        router.push({ path: '/featureSelection/VariableDetail' });
        localStorage.setItem('viewDetail', JSON.stringify(row))
    }

    const editDetail = (row) => {
        showEditRoleDialog.value = true

        editvarForm.firstDimension = row.firstDimension
        editvarForm.secondDimension = row.secondDimension
        editvarForm.thirdDimension = row.thirdDimension
        editvarForm.varType = row.varType
        editvarForm.chName = row.chName
        editvarForm.enName = row.enName
        editvarForm.backTrackType = row.backTrackType
        editvarForm.bizMean = row.bizMean
        editvarForm.dataSource = row.dataSource
        editvarForm.updateFrequency = row.updateFrequency
        editvarForm.confidentialityLevel = row.confidentialityLevel
        editvarForm.earliestTime = row.earliestTime
        editvarForm.latestTime = row.latestTime
        editvarForm.version = row.version
        editvarForm.devStatus = row.devStatus
        editvarForm.currentStatus = row.currentStatus
        editvarForm.varId = row.varId
        editvarForm.proLogic = row.proLogic
        editvarForm.proTime = row.proTime
        editvarForm.sourceTable = row.sourceTable
        editvarForm.callCount = row.callCount
        editvarForm.logicFormulation = row.logicFormulation
        editvarForm.process = row.process
        editvarForm.maintainer = row.maintainer
        editvarForm.logicFormulation = row.logicFormulation
        editvarForm.process = row.process
        editvarForm.storedProcedures = row.storedProcedures
        editvarForm.tName = row.tName
        editvarForm.varFormat = row.varFormat
        editvarForm.remark = row.remark
        editvarForm.coverage = row.coverage
        editvarForm.id = row.id
        console.log(row, 'editDetail')
    }

    // 重置方法
    const handleReset = () => {
        filterForm.firstDimension = ''
        filterForm.secondDimension = ''
        filterForm.thirdDimension = ''
        filterForm.variableType = ''
        filterForm.cnName = ''
        filterForm.enName = ''
        filterForm.backTrackType = undefined
        currentPage.value = 1
        pageSize.value = 10
        localStorage.removeItem('params')
        handleSearch()
    }

    // 页面加载时获取变量列表
    onMounted(() => {

        let params = JSON.parse(localStorage.getItem('params'))
        filterForm.firstDimension = params?.firstDimension
        filterForm.secondDimension = params?.secondDimension
        filterForm.thirdDimension = params?.thirdDimension
        filterForm.variableType = params?.variableType
        filterForm.cnName = params?.cnName
        filterForm.enName = params?.enName
        currentPage.value = params?.pageNum || 1
        pageSize.value = params?.pageSize || 10

        //一级维度选项
        getfirstDimensionOptions({
            dictType: 'biz_first_dimension'
        }).then(res => {
            if (res.code == 200) {
                firstDimensionOptions.value = res.data
                console.log(res)
            }
        }).catch(err => {
            console.log('err', err)
        })

        //变量类型选项
        getDataList({
            dictType: 'biz_var_type'
        }).then(res => {
            if (res.code == 200) {
                variableTypeOptions.value = res.data
                console.log(res, 'biz_var_type')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //回溯状态 back_track_type
        getDataList({
            dictType: 'back_track_type'
        }).then(res => {
            if (res.code == 200) {
                huisuo_statu_options.value = res.data
                console.log(res, 'back_track_type')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //更新频率
        getDataList({
            dictType: 'biz_update_frequency'
        }).then(res => {
            if (res.code == 200) {
                update_frequency_options.value = res.data
                console.log(res, 'biz_update_frequency')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //变量密级
        getDataList({
            dictType: 'biz_confidentiality_level'
        }).then(res => {
            if (res.code == 200) {
                variable_confidentiality_options.value = res.data
                console.log(res, 'biz_confidentiality_level')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //biz_data_source 
        getDataList({
            dictType: 'biz_confidentiality_level'
        }).then(res => {
            if (res.code == 200) {
                variable_confidentiality_options.value = res.data
                console.log(res, 'biz_confidentiality_level')
            }
        }).catch(err => {
            console.log('err', err)
        })


        //数据来源 
        getDataList({
            dictType: 'biz_data_source'
        }).then(res => {
            if (res.code == 200) {
                dataSource_options.value = res.data
                console.log(res, '数据来源')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //开发状态 
        getDataList({
            dictType: 'biz_dev_status'
        }).then(res => {
            if (res.code == 200) {
                kaifa_status_options.value = res.data
                console.log(res, '开发状态')
            }
        }).catch(err => {
            console.log('err', err)
        })

        //当前状态 
        getDataList({
            dictType: 'biz_current_status'
        }).then(res => {
            if (res.code == 200) {
                danqian_status_options.value = res.data
                console.log(res, '当前状态')
            }
        }).catch(err => {
            console.log('err', err)
        })


        fetchVariables()
    })
</script>

<style scoped>
    .variable-dictionary {
        padding: 20px;
        background-color: #f5f7fa;
        min-height: 100vh;
    }

    .filter-card,
    .table-card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
    }

    .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
    }

    .total-count {
        font-size: 14px;
        color: #909399;
    }

    .filter-form {
        padding: 20px 0;
    }

    .action-buttons {
        display: flex;
        justify-content: flex-start;
        margin-top: 4px;
    }

    .pagination-container {
        display: flex;
        justify-content: flex-end;
        padding: 20px 0;
    }

    :deep(.el-card__header) {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
        background-color: #fafafa;
    }

    .custom-pagination {
        --el-pagination-button-bg-color: #fff;
        --el-pagination-hover-color: #409eff;
        display: flex;
        justify-content: flex-end;
        padding: 16px 0;
    }

    :deep(.el-table) {
        --el-table-border-color: #ebeef5;
        --el-table-header-bg-color: #f5f7fa;
    }

    :deep(.el-table th) {
        font-weight: 600;
        background-color: #f5f7fa;
    }

    :deep(.el-form-item__label) {
        font-weight: 500;
    }

    :deep(.el-button) {
        padding: 8px 16px;
    }

    :deep(.el-input__wrapper),
    :deep(.el-select) {
        width: 100%;
    }

    :deep(.el-pagination) {
        justify-content: flex-end;
        padding: 0;
    }

    :deep(.el-dialog__body) {
        max-height: 600px;
        overflow-y: scroll;
    }

    .custom-pagination {
        --el-pagination-button-bg-color: #fff;
        --el-pagination-hover-color: #409eff;
        display: flex;
        justify-content: flex-end;
        padding: 16px 0;
    }

    .operation-btn {
        display: flex;
        justify-content: space-between;
    }
</style>