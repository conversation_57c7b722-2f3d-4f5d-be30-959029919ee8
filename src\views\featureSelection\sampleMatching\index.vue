<script setup>
  import { ref, reactive, onMounted, getCurrentInstance } from "vue";
  import { ElMessage } from "element-plus";
  import { genFileId } from "element-plus";
  import usePermissionStore from "@/store/modules/permission";
  import axios from "axios";
  import { dayjs } from 'element-plus'
  import { decryptsm4, encryptsm4 } from "@/utils/sm4Jsencrypt";
  import {
    viewVarDictPage,
    removeAmpleMatchResult,
    getconfig,
    startMatchingFn,
    ampleMatchResultFn,
    exportDown
  } from "@/api/featureSelection/sampleMatching";
  const { proxy } = getCurrentInstance();
  const router = useRouter();
  const routes = computed(() => usePermissionStore().routes);
  console.log("routes:", routes.value);

  const AC2024 = ref("AC2024");
  // 已选变量列表
  const selectedVariables = ref([]);

  // 匹配表单
  const matchForm = reactive({
    creditCode: "",
    traceDate: "",
    customTableName: "",
  });

  // 获取当前日期（纯数字格式：YYYYMMDD）
  const currentDate = new Date();
  const formattedDate = ref(
    `${currentDate.getFullYear()}${String(currentDate.getMonth() + 1).padStart(
      2,
      "0"
    )}${String(currentDate.getDate()).padStart(2, "0")}`
  );

  // 匹配结果
  const matchResults = ref([]);

  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);

  const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchRolesData();
  };

  const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchRolesData();
  };
  const totalLen = ref(0);
  const serialNumber = ref("");
  const loading = ref(false);
  const uuid = ref("");
  const fileList = ref([]);

  // 是否显示已上传变量弹窗
  const showUploadedVariables = ref(false);

  // 是否显示上传示例弹窗
  const showUploadExample = ref(false);

  // 是否显示样本示例弹窗
  const showSampleExample = ref(false);

  // 导入变量
  const handleImportVariables = () => {
    const fileInput = document.getElementById("import-file");
    fileInput.value = "";

    fileInput.click();
  };

  const currentPage2 = ref(1);
  const pageSize2 = ref(10);
  const total2 = ref(0);

  const handleSizeChange2 = (val) => {
    pageSize2.value = val;
    currentPage2.value = 1;
    fetchRolesData2();
  };

  const handleCurrentChange2 = (val) => {
    currentPage2.value = val;
    fetchRolesData2();
  };

  const fetchRolesData2 = () => {
    let params = {
      serialNumber: serialNumber.value,
      pageNum: currentPage2.value,
      pageSize: pageSize2.value,
    };
    ampleMatchResultFn(params).then((res) => {
      matchResults.value = res.rows;
      total2.value = res.total;
      console.log(res, "ampleMatchResult");
    });
  };

  // 文件选择变化
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleUpload(file);
      e.target.value = "";
    }
  };

  // 处理文件上传
  const handleUpload = (file) => {
    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    let config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: "Bearer " + localStorage.getItem("Admin-Token"),
      },
    };

    // 显示全屏加载动画
    proxy.$modal.loading("导入变量中，请稍候...");

    axios
      .post(
        import.meta.env.VITE_APP_BASE_API + "/biz/ampleMatchResult/uploadVarDict",
        formData,
        config
      )
      .then((res) => {
        console.log("res:",res);
        if (res.data.code == 200) {
          totalLen.value = decryptsm4(res.data.data)?.total;
          serialNumber.value = decryptsm4(res.data.data)?.serialNumber
            ? decryptsm4(res.data.data)?.serialNumber
            : "";
          ElMessage.success(res.data.msg);

          upload.value.clearFiles();
          console.log('clearFiles')
          fileList.value = [];

          upload.value.clearFiles();
          matchForm.customTableName = undefined

          getconfig().then((res) => {
            console.log(res, "getconfig");
            if (res.code == 200) {
              config.account = res.data?.account;
              config.date = res.data?.date;
            }
          });
        } else {
          ElMessage.error(res.data.msg);
        }
      })
      .catch((error) => {
        console.error("Upload error:", error);
        ElMessage.error(error.msg);
        fileList.value = [];
      })
      .finally(() => {
        // 关闭全屏加载动画
        proxy.$modal.closeLoading();
      });
  };

  // 上传前校验
  const beforeUpload = (file) => {
    if (!serialNumber.value) {
      ElMessage.warning("请先上传导入已选变量文件!");
      return;
    }
    const isValidType = [
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/csv",
    ].includes(file.type);
    if (!isValidType) {
      ElMessage.error("只能上传 Excel 或 CSV 文件!");
      return false;
    }
    return true;
  };

  const config = reactive({
    account: "",
    date: "",
  });

  const handleRemove = (file, uploadFiles) => {
    // console.log(file, uploadFiles);
    // fileList.value = [];
    uuid.value = undefined
    upload.value.clearFiles();
    matchForm.customTableName = undefined
    console.log('handleRemove:', handleRemove)
  };

  const clickFn = () => {
    if (!serialNumber.value) {
      ElMessage.warning("请先上传导入已选变量文件!");
      return false;
    }
  };

  const handleChange = (uploadFile, uploadFiles) => {
    // if (!serialNumber.value) {
    //   ElMessage.warning("请先上传导入已选变量文件!");
    //   return false;
    // }
    console.log(uploadFile, uploadFiles);
    //上传文件
    const formData = new FormData();
    formData.append("file", uploadFile.raw);
    formData.append("serialNumber", serialNumber.value);
	// 显示全屏加载动画
	proxy.$modal.loading("上传中，请稍候...");

    let config = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: "Bearer " + localStorage.getItem("Admin-Token"),
      },
    };

    axios
      .post(
        import.meta.env.VITE_APP_BASE_API + "/biz/ampleMatchResult/uploadAmple",
        formData,
        config
      )
      .then((res) => {
        console.log(res, 'res')
        if (res.data.code == 200) {
          uuid.value = decryptsm4(res.data.data)?.uuid;
          ElMessage.success("文件上传成功");
		  // 关闭全屏加载动画
		  proxy.$modal.closeLoading();
        } else {
          ElMessage.error(res.data.msg);
          upload.value.clearFiles();
		  // 关闭全屏加载动画
		  proxy.$modal.closeLoading();
        }
      })
      .catch((error) => {
        console.error("Upload error:", error);
        ElMessage.error("文件上传失败");
		// 关闭全屏加载动画
		proxy.$modal.closeLoading();
      });
    // fileList.value = uploadFiles.slice(-1)
  };

  // 开始匹配
  const startMatching = () => {
    // 这里应该调用API开始匹配
    // console.log('开始匹配', matchForm)
    if (!serialNumber.value) {
      ElMessage.warning("请先上传导入已选变量文件!");
      return false;
    }
    if (!uuid.value) {
      ElMessage.warning("请先上传样本文件!");
      return false;
    }
    if (!matchForm.customTableName) {
      ElMessage.warning("请先填写自定义表名!");
      return false;
    }
    let params = {
      tName: matchForm.customTableName,
      uuid: uuid.value,
      serialNumber: serialNumber.value,
    };
    startMatchingFn(params)
      .then((res) => {
        fetchRolesData2();
        console.log(res, "===样本");
      })
      .catch((err) => {
        console.log(err, " err");
        fetchRolesData2();
      });
    ElMessage.success("开始匹配");
  };

  const reloadBtn = () => {
    fetchRolesData2();
  };

  // 删除已选变量
  const removeVariable = (id) => {
    removeAmpleMatchResult({
      id: id,
      serialNumber: serialNumber.value,
    }).then((res) => {
      if (res.code == 200) {
        fetchRolesData();
        ElMessage.success("删除成功");
      }
    });
    // const index = selectedVariables.value.findIndex(item => item.id === id)
    // if (index !== -1) {
    //   selectedVariables.value.splice(index, 1)
    //   ElMessage.success('删除成功')
    // }
  };

  // 查看已上传变量
  const viewUploadedVariables = () => {
    console.log("serialNumber.value", serialNumber.value);

    if (!serialNumber.value) {
      ElMessage.warning("请先上传导入已选变量文件!");
      return;
    }
    showUploadedVariables.value = true;
    fetchRolesData();
  };

  const fetchRolesData = () => {
    loading.value = true;
    try {
      // console.log('获取角色数据...');
      let params = {
        serialNumber: serialNumber.value,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      };
      viewVarDictPage(params).then((res) => {
        console.log(res, "viewVarDictPage");
        if (res.code == 200) {
          selectedVariables.value = res.rows;
          total.value = res.total;
          totalLen.value = res.total;
          loading.value = false;
        }
      });
      // 在实际应用中应调用API
      // roles.value = await api.getRoles();
    } finally {
    }
  };

  // 查看上传示例
  const viewUploadExample = () => {
    showUploadExample.value = true;
  };

  // 查看样本示例
  const viewSampleExample = () => {
    showSampleExample.value = true;
  };

  // 关闭弹窗
  const closeModal = () => {
    showUploadedVariables.value = false;
    showUploadExample.value = false;
    showSampleExample.value = false;
  };
  onMounted(() => {
    getconfig().then((res) => {
      console.log(res, "getconfig");
      if (res.code == 200) {
        config.account = res.data?.account;
        config.date = res.data?.date;
      }
    });
    fetchRolesData2();
  });

  const upload = ref();

  const handleExceed = (files) => {
    upload.value.clearFiles();
    const file = files[0];
    file.uid = genFileId();
    upload.value.handleStart(file);
  };




  /** 下载按钮操作 */
  const handleDownload = async (item) => {
    if (!serialNumber.value) {
      ElMessage.warning("请先上传导入已选变量文件!");
      return;
    }
    //文件后缀
    //   const fileExtension = item.filePath.slice(item.filePath.lastIndexOf('.') + 1)
    try {
      let result = await exportDown({
        serialNumber: serialNumber.value
      });

      console.log(result, 'result')

      const blob = new Blob([result], {
        type: 'application/vnd.ms-excel',
      })
      let date = new Date()
      date = dayjs(date).format('YYYY-MM-DD HH:mm:ss')
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `样本匹配模版-${date}.xlsx`
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      // console.log('error: ', err)
    }
  }


  // onMounted(() => {
  //   // 页面加载时获取数据
  //   // 这里可以调用API获取数据
  // })
</script>

<template>
  <div class="sample-matching-container">
    <!-- 已选变量展示 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <h2 class="section-title">已选变量列表</h2>
          <div class="action-buttons">
            <el-button type="success" @click="viewUploadExample" class="action-button">
              <el-icon>
                <document />
              </el-icon>查看上传示例
            </el-button>
            <el-button type="primary" @click="handleImportVariables" v-hasPermi="['filter:match:uploadVarDict']"
              class="action-button">
              <el-icon><el-icon>
                  <Upload />
                </el-icon></el-icon>导入已选变量
            </el-button>
            <el-button type="warning" @click="viewUploadedVariables" v-hasPermi="['filter:match:varDictPage']"
              class="action-button">
              <el-icon>
                <list />
              </el-icon>查看已上传变量
            </el-button>
            <input id="import-file" type="file" accept=".xlsx,.xls" class="hidden" @change="handleFileChange"
              style="display: none" />
          </div>
        </div>
      </template>
      <el-alert :title="`已选择 ${totalLen} 个变量，点击'查看已上传变量'可查看详情。`" type="info" :closable="false" class="info-alert"
        v-if="totalLen" />
    </el-card>

    <!-- 样本上传区域 -->
    <el-card class="section-card">
      <template #header>
        <div style="display: flex; justify-content: space-between;">
          <h2 class="section-title">样本上传</h2>
          <el-button type="primary" @click="handleDownload" class="action-button">
            下载模版
          </el-button>
        </div>
      </template>

      <div class="upload-section">
        <!-- 1. 样本示例 -->
        <div class="upload-step">
          <h3 class="step-title">
            <el-icon>
              <document />
            </el-icon>
            1. 样本示例
          </h3>

          <el-button type="primary" @click="viewSampleExample" class="step-button">
            <el-icon>
              <document />
            </el-icon>查看样本示例
          </el-button>
        </div>

        <!-- 2. 样本上传 -->
        <div class="upload-step">
          <h3 class="step-title">
            <el-icon>
              <upload />
            </el-icon>
            2. 样本上传
          </h3>
          <el-upload class="upload-area" ref="upload" :before-upload="beforeUpload" drag action="#" :limit="1"
            :on-exceed="handleExceed" :auto-upload="false" :on-change="handleChange" accept=".csv,.xlsx,.xls"
            :disabled="!serialNumber" @click="clickFn" :on-remove="handleRemove">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">支持 csv、xlsx格式</div>
            </template>
          </el-upload>
        </div>

        <!-- 3. 自定义表名 -->
        <div class="upload-step">
          <h3 class="step-title">
            <el-icon>
              <edit />
            </el-icon>
            3. 自定义表名
          </h3>
          <el-form class="table-name-form">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="账号">
                  <el-input v-model="config.account" disabled class="custom-input" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="日期">
                  <el-input v-model="config.date" disabled class="custom-input" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="自定义表名">
                  <el-input v-model="matchForm.customTableName" placeholder="请输入自定义表名" class="custom-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 4. 开始匹配 -->
        <div class="upload-step">
          <h3 class="step-title">
            <el-icon>
              <play />
            </el-icon>
            4. 开始匹配
          </h3>
          <div class="start-matching">
            <el-button type="primary" size="large" @click="startMatching" class="start-button"
              v-hasPermi="['filter:match:startMatching']">
              <el-icon><video-play /></el-icon>开始匹配
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 匹配结果展示 -->
    <el-card class="section-card">
      <template #header>
        <div class="top-body">
          <h2 class="section-title">匹配结果</h2>

          <el-button type="primary" size="large" @click="reloadBtn" v-hasPermi="['filter:match:page']">
            刷新
          </el-button>
        </div>
      </template>
      <el-table :data="matchResults" style="width: 100%" class="result-table">
        <el-table-column prop="tableName" label="表名" min-width="200" />
        <el-table-column prop="createTime" label="开始时间" min-width="180" />
        <el-table-column prop="sampleCount" label="样本数量" min-width="120" />
        <el-table-column prop="matchCount" label="匹配数量" min-width="120" />
        <el-table-column prop="errorReason" label="报错原因" min-width="200" />
        <el-table-column prop="status" label="状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.status == 0
              ? 'success'
              : row.status == '1'
                ? 'danger'
                : 'warning'
              " class="status-tag">
              {{ row.statusStr }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="mt-6 flex justify-end">
        <el-pagination v-model:current-page="currentPage2" v-model:page-size="pageSize2" :total="total2"
          :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange2"
          @current-change="handleCurrentChange2" class="custom-pagination" />
      </div>
    </el-card>

    <!-- 查看已上传变量弹窗 -->
    <el-dialog v-model="showUploadedVariables" title="已上传变量" width="70%">
      <el-table :data="selectedVariables" style="width: 100%" v-loading="loading">
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="chName" label="变量中文名" />
        <el-table-column prop="enName" label="变量英文名" />
		<el-table-column prop="currentStatus" label="当前状态" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="danger" link @click="removeVariable(row.id)"
              v-hasPermi="['filter:match:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="mt-6 flex justify-end">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[5, 10, 20, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" class="custom-pagination" />
      </div>
    </el-dialog>

    <!-- 查看上传示例弹窗 -->
    <el-dialog v-model="showUploadExample" title="上传示例" width="50%">
      <div class="text-center">
        <p class="text-gray-600 mb-2">Excel文件格式示例</p>
        <img src="@/assets/images/upload.png" alt="上传示例" class="max-w-full h-auto" />
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 100px; text-align: left;">
          请按照示例格式准备Excel文件，包含变量中文名、变量英文名等信息
        </p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 100px; text-align: left;">
          1、导入已选变量时必须包含上述两个字段（英文名、中文名）
        </p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 100px; text-align: left;">
          2、可直接上传通过变量筛选下载的字典</p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 100px; text-align: left;">
          3、单次建表上传的字段个数应不超过500个</p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 100px; text-align: left;">
          4、尽量将相同三级维度的特征变量在同一批次上传，可提升匹配效率</p>
      </div>
    </el-dialog>

    <!-- 查看样本示例弹窗 -->
    <el-dialog v-model="showSampleExample" title="样本示例" width="50%">
      <div class="text-center">
        <p class="text-gray-600 mb-2">样本文件格式示例</p>
        <img src="@/assets/images/upload2.png" alt="样本示例" class="max-w-full h-auto" />
        <p class="text-sm text-gray-500 mt-2">
          请按照示例格式准备样本文件，包含企业统一社会信用代码等信息
        </p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 60px;">1、通过下载模板获取待上传样本的文件格式，填充样本时不要改变列的顺序</p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 60px;">
          2、第二列时间格式可支持多种形式上传匹配，例如：2025-01-01、2025/1/1</p>
        <p class="text-sm text-gray-500 mt-2">
          3、变量匹配时是根据回溯时间的当月一号在数据库中进行查找匹配</p>
        <p class="text-sm text-gray-500 mt-2" style="padding-left: 47px;">
          4、单次建表上传的样本个数应不超过5万条，若超过可分为多个表进行落库</p>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
  .sample-matching-container {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  .section-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .card-header {
    display: flex;
    /* justify-content: space-between;
  align-items: center; */
    justify-content: space-between;
    padding: 16px 0;
    flex-direction: column;
  }

  .card-header h2 {
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }

  .action-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .info-alert {
    margin: 16px 0;
  }

  .upload-section {
    padding: 16px 0;
  }

  .upload-step {
    margin-bottom: 32px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .step-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
  }

  .step-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .upload-area {
    width: 100%;
  }

  .table-name-form {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
  }

  .custom-input {
    width: 100%;
  }

  .start-matching {
    display: flex;
    justify-content: center;
    padding: 24px 0;
  }

  .start-button {
    padding: 12px 32px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .result-table {
    margin-top: 16px;
  }

  .status-tag {
    padding: 4px 12px;
    border-radius: 4px;
  }

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-table th) {
    background-color: #f5f7fa;
    font-weight: 600;
  }

  .custom-pagination {
    --el-pagination-button-bg-color: #fff;
    --el-pagination-hover-color: #409eff;
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }

  .top-body {
    display: flex;
    justify-content: space-between;

    /* padding: 8px 0; */
    h2 {
      display: flex;
      align-items: center;
    }
  }
</style>