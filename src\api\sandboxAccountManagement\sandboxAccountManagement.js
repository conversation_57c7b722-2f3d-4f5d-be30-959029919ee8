import request from '@/utils/request'



// 沙箱账号列表查询
export function getsandboxUserList(data) {
  return request({
    url: '/biz/sandboxUser/page',
    method: 'post',
    data: data
  })
}

//7.3.2.新增沙箱账号
export function addSandboxUser(data) {
  return request({
    url: '/biz/sandboxUser/add',
    method: 'post',
    data: data
  })
}

//7.3.3.禁用启用
export function editSandboxAccountManagementStatus(data) {
  return request({
    url: '/biz/sandboxUser/status',
    method: 'post',
    data: data
  })
}

//7.3.4.修改密码
export function editPassword(data) {
  return request({
    url: '/biz/sandboxUser/resetPassword',
    method: 'post',
    data: data
  })
}

