<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { handleThemeStyle } from "@/utils/theme";
// import Layout from "@/layout/index";
// import ParentView from "@/components/ParentView";
// import InnerLink from "@/layout/components/InnerLink";
// const sdata = [

//              {
//               name: "DataVisualization",
//               path: "/DataVisualization",
//               redirect: "/DataVisualization/dataBigScreen",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "数据可视化",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "dataBigScreen",
//                   path: "dataBigScreen",
//                   hidden: false,
//                   component: "DataVisualization/dataBigScreen/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "数据大屏",
//                   },
//                 },
//                 {
//                   name: "variableCoverageStatistics",
//                   path: "variableCoverageStatistics",
//                   hidden: false,
//                   component: "DataVisualization/variableCoverageStatistics/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "变量覆盖统计",
//                   },
//                 },
//               ],
//             },


//             {
//               name: "featureSelection",
//               path: "/featureSelection",
//               redirect: "/featureSelection/variableDictionary",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "特征筛选",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "variableDictionary",
//                   path: "variableDictionary",
//                   hidden: false,
//                   component: "featureSelection/variableDictionary/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "变量字典",
//                   },
//                 },
//                 {
//                   name: "variableDerivation",
//                   path: "variableDerivation",
//                   hidden: false,
//                   component: "featureSelection/variableDerivation/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "变量衍生",
//                   },
//                 },
//               {
//                   name: "sampleMatching",
//                   path: "sampleMatching",
//                   hidden: false,
//                   component: "featureSelection/sampleMatching/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "样本匹配",
//                   },
//                 },
//               ],
//             },

//               {
//               name: "featureManagement",
//               path: "/featureManagement",
//               redirect: "/featureManagement/VariableDictionaryManagement",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "特征管理",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "VariableDictionaryManagement",
//                   path: "VariableDictionaryManagement",
//                   hidden: false,
//                   component: "featureManagement/VariableDictionaryManagement/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "变量字典管理",
//                   },
//                 },
//                 {
//                   name: "StoredProcedureManagement",
//                   path: "StoredProcedureManagement",
//                   hidden: false,
//                   component: "featureManagement/StoredProcedureManagement/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "存储过程管理",
//                   },
//                 },
//               {
//                   name: "TaskSchedulingManagement",
//                   path: "TaskSchedulingManagement",
//                   hidden: false,
//                   component: "featureManagement/TaskSchedulingManagement/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "任务调度管理",
//                   },
//                 },
//               ],
//             },


//               {
//               name: "ModelDeployment",
//               path: "/ModelDeployment",
//               redirect: "/ModelDeployment/ModelingHistory",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "模型部署",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "ModelingHistory",
//                   path: "ModelingHistory",
//                   hidden: false,
//                   component: "ModelDeployment/ModelingHistory/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "建模历史",
//                   },
//                 },
//                 {
//                   name: "ModelList",
//                   path: "ModelList",
//                   hidden: false,
//                   component: "ModelDeployment/ModelList/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "模型列表",
//                   },
//                 },
//               {
//                   name: "StrategyHistory",
//                   path: "StrategyHistory",
//                   hidden: false,
//                   component: "ModelDeployment/StrategyHistory/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "策略历史",
//                   },
//                 },
//                    {
//                   name: "StrategyList",
//                   path: "StrategyList",
//                   hidden: false,
//                   component: "ModelDeployment/StrategyList/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "策略列表",
//                   },
//                 },
//               ],
//             },


//              {
//               name: "IndicatorMonitoring",
//               path: "/IndicatorMonitoring",
//               redirect: "/IndicatorMonitoring/SingleTableWarning",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "指标监控",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "SingleTableWarning",
//                   path: "SingleTableWarning",
//                   hidden: false,
//                   component: "IndicatorMonitoring/SingleTableWarning/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "预警推送-单表预警",
//                   },
//                 },
//                 {
//                   name: "MemoryAlert",
//                   path: "MemoryAlert",
//                   hidden: false,
//                   component: "IndicatorMonitoring/MemoryAlert/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "预警推送-内存预警",
//                   },
//                 },
//               {
//                   name: "BatchProgress",
//                   path: "BatchProgress",
//                   hidden: false,
//                   component: "IndicatorMonitoring/BatchProgress/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "任务监控-跑批进度",
//                   },
//                 },
//               ],
//             },

//             {
//               name: "systemManagement",
//               path: "/systemManagement",
//               redirect: "/systemManagement/accountManagement",
//               hidden: false,
//               meta: {
//                 icon: "icon_picture",
//                 isLeft: "0",
//                 leftLevel: null,
//                 link: null,
//                 noCache: false,
//                 projectType: null,
//                 title: "系统管理",
//               },
//               alwaysShow: true,
//               component: Layout,
//               children: [
//                 {
//                   name: "accountManagement",
//                   path: "accountManagement",
//                   hidden: false,
//                   component: "systemManagement/accountManagement/index.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "0",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "账号管理",
//                   },
//                 },
//                 {
//                   name: "roleManagement",
//                   path: "roleManagement",
//                   hidden: false,
//                   component: "systemManagement/roleManagement/indexindex.vue",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "角色管理",
//                   },
//                 },
//               {
//                   name: "sandboxAccountManagement",
//                   path: "sandboxAccountManagement",
//                   hidden: false,
//                   component: "systemManagement/sandboxAccountManagement/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "沙箱账号管理",
//                   },
//                 },
//                     {
//                   name: "sandboxAccount",
//                   path: "sandboxAccount",
//                   hidden: false,
//                   component: "systemManagement/sandboxAccount/index",
//                   meta: {
//                     icon: "xitongguanli",
//                     isLeft: "1",
//                     leftLevel: null,
//                     link: null,
//                     noCache: false,
//                     projectType: null,
//                     title: "沙箱账号",
//                   },
//                 },
//               ],
//             },
           
//           ];

          // 遍历后台传来的路由字符串，转换为组件对象
// function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
//   return asyncRouterMap.filter((route) => {
//     if (type && route.children) {
//       route.children = filterChildren(route.children);
//     }
//     if (route.component) {
//       // Layout ParentView 组件特殊处理
//       if (route.component === "Layout") {
//         route.component = Layout;
//       } else if (route.component === "ParentView") {
//         route.component = ParentView;
//       } else if (route.component === "InnerLink") {
//         route.component = InnerLink;
//       } else {
//         route.component = loadView(route.component);
//       }
//     }
//     if (route.children != null && route.children && route.children.length) {
//       route.children = filterAsyncRouter(route.children, route, type);
//     } else {
//       delete route["children"];
//       delete route["redirect"];
//     }
//     return true;
//   });
// }

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
    // const sidebarRoutes = filterAsyncRouter(sdata);
    // this.setDefaultRoutes(sidebarRoutes);
  });
});
</script>

<style>
p {
  margin-top: 0;
}
</style>
