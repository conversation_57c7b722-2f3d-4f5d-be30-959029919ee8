<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { procedureApi } from '../utils/api'

const router = useRouter()

// 加载状态
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  name: '',
  tables: '',
  description: '',
  version: '',
  maintainAccount: ''
})

// 表格数据
const tableData = ref([])

// 获取所有存储过程
const fetchProcedures = async () => {
  loading.value = true
  try {
    // const response = await procedureApi.getProcedures()
    // if (response && Array.isArray(response)) {
    //   tableData.value = response
    // } else {
    //   tableData.value = [] // 确保tableData始终是数组
    //   console.error('API返回的数据格式不正确:', response)
    // }
  } catch (error) {
    tableData.value = [] // 错误时也确保tableData是空数组
    console.error('获取存储过程列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = async () => {
  loading.value = true
  try {
    // 将tables字符串转换为数组
    const params = { ...filterForm }
    if (params.tables) {
      params.tables = params.tables.split(',').map(item => item.trim())
    }
    const response = await procedureApi.filterProcedures(params)
    if (response && Array.isArray(response)) {
      tableData.value = response
    } else {
      tableData.value = [] // 确保tableData始终是数组
      console.error('API返回的数据格式不正确:', response)
    }
  } catch (error) {
    tableData.value = [] // 错误时也确保tableData是空数组
    console.error('筛选存储过程失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看明细
const viewDetail = (id) => {
  router.push(`/procedure-detail/${id}`)
}

// 页面加载时获取存储过程列表
onMounted(() => {
  fetchProcedures()
})
</script>

<template>
  <div class="variable-derivation-container">
    <!-- 筛选区域 -->
    <el-card class="filter-card" shadow="hover">

      <el-form :model="filterForm" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="存储过程名称">
              <el-input v-model="filterForm.name" placeholder="请输入存储过程名称" clearable class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表名称">
              <el-input v-model="filterForm.tables" placeholder="请输入表名称，多个用逗号分隔" clearable class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="简介">
              <el-input v-model="filterForm.description" placeholder="请输入简介" clearable class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="版本">
              <el-input v-model="filterForm.version" placeholder="请输入版本" clearable class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护账号">
              <el-input v-model="filterForm.maintainAccount" placeholder="请输入维护账号" clearable class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" style="display: flex;">
              <el-button @click="resetForm" style="float: right;">
                <el-icon>
                  <Refresh />
                </el-icon>重置
              </el-button>
              <el-button type="primary" @click="handleSearch" :loading="loading" style="float: right;">
                <el-icon>
                  <Search />
                </el-icon>查询
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

        

      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <!-- <template #header>
        <div class="card-header">
          <span class="header-title">数据列表</span>
        </div>
      </template> -->
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe highlight-current-row
        class="custom-table">
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="name" label="存储过程名称" min-width="180" show-overflow-tooltip />
        <el-table-column label="表名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag v-for="table in row.tables" :key="table" class="table-tag" size="small">
              {{ table }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="简介" min-width="200" show-overflow-tooltip />
        <el-table-column prop="version" label="版本" width="100" align="center" />
        <el-table-column prop="updateDate" label="更新日期" width="160" align="center" />
        <el-table-column prop="maintainAccount" label="维护账号" width="120" align="center" />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewDetail(row.id)">
              查看明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.variable-derivation-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 40px);

  .filter-card,
  .table-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .filter-form {
    .custom-input {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }
    }

    .form-buttons {
      margin-top: 20px;
      // text-align: center;
      display: flex;
      justify-content: flex-end;

      .el-button {
        padding: 8px 20px;
        margin: 0 10px;
      }
    }
  }

  .custom-table {
    :deep(.el-table__header) {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }
    }

    :deep(.el-table__row) {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f9ff !important;
      }
    }
  }

  .table-tag {
    margin: 2px;
    background-color: #ecf5ff;
    color: #409eff;
    border: 1px solid #d9ecff;
  }
}
</style>