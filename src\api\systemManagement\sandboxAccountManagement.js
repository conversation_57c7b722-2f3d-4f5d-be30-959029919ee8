import request from '@/utils/request'



// 7.3.1.沙箱账号列表查询
export function getSandboxUserList(data) {
  return request({
    url: '/biz/sandboxUser/page',
    method: 'post',
    data: data
  })
}

export function addSandboxUser(data) {
  return request({
    url: '/biz/sandboxUser/add',
    method: 'post',
    data: data
  })
}


export function editSandboxUserStatus(data) {
  return request({
    url: '/biz/sandboxUser/status',
    method: 'post',
    data: data
  })
}

export function editSandboxUserResetPassword(data) {
  return request({
    url: '/biz/sandboxUser/resetPassword',
    method: 'post',
    data: data
  })
}


export function getSandboxList(data) {
  return request({
    url: '/biz/sandbox/list',
    method: 'post',
    data: data
  })
}


export function getSandboxDataList(data) {
  return request({
    url: '/system/dict/data/dataList',
    method: 'post',
    data: data
  })
}



export function editSandboxFn(data){
  return request({
    url: '/biz/sandboxUser/edit',
    method: 'post',
    data: data
  })
}

export function delSandboxFn(data){
  return request({
    url: '/biz/sandboxUser/remove',
    method: 'post',
    data: data
  })
}