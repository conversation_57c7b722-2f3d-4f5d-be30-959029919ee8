<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'

// 统计数据
const coverageData = ref([
  {
    id: 1,
    firstDimension: '企业基本信息',
    secondDimension: '工商信息',
    thirdDimension: '注册信息',
    variableId: 'ENT001',
    variableCnName: '注册日期',
    variableEnName: 'reg_date',
    statisticMonth: '2024年1月',
    coverageRatio: '98.5%',
    tableUpdateProgress: '2024年1月',
    variableUpdateProgress: '2024年1月'
  },
  {
    id: 2,
    firstDimension: '企业基本信息',
    secondDimension: '工商信息',
    thirdDimension: '股东信息',
    variableId: 'ENT002',
    variableCnName: '股东名称',
    variableEnName: 'shareholder',
    statisticMonth: '2024年1月',
    coverageRatio: '95.2%',
    tableUpdateProgress: '2024年1月',
    variableUpdateProgress: '2024年1月'
  },
  {
    id: 3,
    firstDimension: '经营状况',
    secondDimension: '税务信息',
    thirdDimension: '纳税情况',
    variableId: 'TAX001',
    variableCnName: '纳税金额',
    variableEnName: 'tax_amount',
    statisticMonth: '2024年1月',
    coverageRatio: '92.8%',
    tableUpdateProgress: '2023年12月',
    variableUpdateProgress: '2023年12月'
  },
  {
    id: 4,
    firstDimension: '经营状况',
    secondDimension: '税务信息',
    thirdDimension: '发票信息',
    variableId: 'TAX002',
    variableCnName: '发票数量',
    variableEnName: 'invoice_count',
    statisticMonth: '2024年1月',
    coverageRatio: '91.5%',
    tableUpdateProgress: '2023年12月',
    variableUpdateProgress: '2023年12月'
  },
  {
    id: 5,
    firstDimension: '信用记录',
    secondDimension: '司法信息',
    thirdDimension: '诉讼记录',
    variableId: 'JUD001',
    variableCnName: '诉讼次数',
    variableEnName: 'lawsuit_count',
    statisticMonth: '2024年1月',
    coverageRatio: '89.7%',
    tableUpdateProgress: '2024年1月',
    variableUpdateProgress: '2024年1月'
  }
])

// 筛选表单
const filterForm = ref({
  firstDimension: '',
  secondDimension: '',
  thirdDimension: '',
  month: '2024年1月'
})

// 一级维度选项
const firstDimensionOptions = [
  { value: '', label: '全部' },
  { value: '企业基本信息', label: '企业基本信息' },
  { value: '经营状况', label: '经营状况' },
  { value: '信用记录', label: '信用记录' }
]

// 二级维度选项
const secondDimensionOptions = [
  { value: '', label: '全部' },
  { value: '工商信息', label: '工商信息' },
  { value: '税务信息', label: '税务信息' },
  { value: '司法信息', label: '司法信息' }
]

// 三级维度选项
const thirdDimensionOptions = [
  { value: '', label: '全部' },
  { value: '注册信息', label: '注册信息' },
  { value: '股东信息', label: '股东信息' },
  { value: '变更记录', label: '变更记录' },
  { value: '纳税情况', label: '纳税情况' },
  { value: '发票信息', label: '发票信息' },
  { value: '诉讼记录', label: '诉讼记录' }
]

// 统计月份选项
const monthOptions = [
  { value: '2024年1月', label: '2024年1月' },
  { value: '2023年12月', label: '2023年12月' },
  { value: '2023年11月', label: '2023年11月' }
]

// 查询数据
const handleSearch = () => {
  console.log('查询条件：', filterForm.value)
  ElMessage.success('查询成功')
  // 这里应该调用API获取数据
}

// 下载数据
const handleDownload = () => {
  console.log('下载数据')
  ElMessage.success('下载成功')
  // 这里应该调用API下载数据
}

// 根据覆盖率返回标签类型
const getCoverageTagType = (ratio) => {
  const value = parseFloat(ratio)
  if (value >= 95) return 'success'
  if (value >= 90) return 'warning'
  return 'danger'
}

onMounted(() => {
  // 页面加载时获取数据
  // 这里可以调用API获取数据
})
</script>

<template>
  <div class="variable-coverage-container">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">变量覆盖率统计</span>
        </div>
      </template>
      <el-form :model="filterForm" label-width="100px" class="filter-form">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :md="6">
            <el-form-item label="一级维度">
              <el-select v-model="filterForm.firstDimension" placeholder="请选择" class="w-full" style="width: 100%;">
                <el-option
                  v-for="option in firstDimensionOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <el-form-item label="二级维度">
              <el-select v-model="filterForm.secondDimension" placeholder="请选择" class="w-full"  style="width: 100%;">
                <el-option
                  v-for="option in secondDimensionOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <el-form-item label="三级维度">
              <el-select v-model="filterForm.thirdDimension" placeholder="请选择" class="w-full"  style="width: 100%;">
                <el-option
                  v-for="option in thirdDimensionOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <el-form-item label="统计月份">
              <el-select v-model="filterForm.month" placeholder="请选择" class="w-full"  style="width: 100%;">
                <el-option
                  v-for="option in monthOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="form-buttons">
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button type="success" @click="handleDownload" :icon="Download">下载</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="coverageData" 
        border 
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 'bold'
        }"
        :cell-style="{
          padding: '12px 0'
        }"
      >
        <el-table-column prop="firstDimension" label="一级维度" min-width="120" />
        <el-table-column prop="secondDimension" label="二级维度" min-width="120" />
        <el-table-column prop="thirdDimension" label="三级维度" min-width="120" />
        <el-table-column prop="variableId" label="ID" min-width="100" />
        <el-table-column prop="variableCnName" label="变量中文名" min-width="120" />
        <el-table-column prop="variableEnName" label="变量英文名" min-width="120" />
        <el-table-column prop="statisticMonth" label="统计月份" min-width="100" />
        <el-table-column prop="coverageRatio" label="覆盖度占比" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getCoverageTagType(row.coverageRatio)"
              effect="light"
              class="coverage-tag"
            >
              {{ row.coverageRatio }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="tableUpdateProgress" label="底表更新进展" min-width="120" />
        <el-table-column prop="variableUpdateProgress" label="变量更新进展" min-width="120" />
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.variable-coverage-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.filter-card,
.table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.filter-form {
  padding: 10px 0;
}

.form-buttons {
  margin-top: 20px;
  text-align: center;
}

.form-buttons .el-button {
  padding: 12px 24px;
  margin: 0 10px;
}

.coverage-tag {
  font-weight: 500;
  padding: 4px 8px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

@media screen and (max-width: 768px) {
  .variable-coverage-container {
    padding: 10px;
  }
  
  .form-buttons .el-button {
    width: 100%;
    margin: 10px 0;
  }
  .w-full {
    width: 100% !important;
  }
}
</style> 